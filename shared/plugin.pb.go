// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: plugin.proto

package shared

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 空消息
type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_plugin_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{0}
}

// 字符串响应
type StringResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StringResponse) Reset() {
	*x = StringResponse{}
	mi := &file_plugin_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringResponse) ProtoMessage() {}

func (x *StringResponse) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringResponse.ProtoReflect.Descriptor instead.
func (*StringResponse) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{1}
}

func (x *StringResponse) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 初始化请求
type InitializeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Config        map[string]string      `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitializeRequest) Reset() {
	*x = InitializeRequest{}
	mi := &file_plugin_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitializeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitializeRequest) ProtoMessage() {}

func (x *InitializeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitializeRequest.ProtoReflect.Descriptor instead.
func (*InitializeRequest) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{2}
}

func (x *InitializeRequest) GetConfig() map[string]string {
	if x != nil {
		return x.Config
	}
	return nil
}

// 执行请求
type ExecuteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Input         string                 `protobuf:"bytes,1,opt,name=input,proto3" json:"input,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteRequest) Reset() {
	*x = ExecuteRequest{}
	mi := &file_plugin_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteRequest) ProtoMessage() {}

func (x *ExecuteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteRequest.ProtoReflect.Descriptor instead.
func (*ExecuteRequest) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{3}
}

func (x *ExecuteRequest) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

// 执行响应
type ExecuteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Output        string                 `protobuf:"bytes,1,opt,name=output,proto3" json:"output,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteResponse) Reset() {
	*x = ExecuteResponse{}
	mi := &file_plugin_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteResponse) ProtoMessage() {}

func (x *ExecuteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteResponse.ProtoReflect.Descriptor instead.
func (*ExecuteResponse) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{4}
}

func (x *ExecuteResponse) GetOutput() string {
	if x != nil {
		return x.Output
	}
	return ""
}

var File_plugin_proto protoreflect.FileDescriptor

const file_plugin_proto_rawDesc = "" +
	"\n" +
	"\fplugin.proto\x12\x06shared\"\a\n" +
	"\x05Empty\"&\n" +
	"\x0eStringResponse\x12\x14\n" +
	"\x05value\x18\x01 \x01(\tR\x05value\"\x8d\x01\n" +
	"\x11InitializeRequest\x12=\n" +
	"\x06config\x18\x01 \x03(\v2%.shared.InitializeRequest.ConfigEntryR\x06config\x1a9\n" +
	"\vConfigEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"&\n" +
	"\x0eExecuteRequest\x12\x14\n" +
	"\x05input\x18\x01 \x01(\tR\x05input\")\n" +
	"\x0fExecuteResponse\x12\x16\n" +
	"\x06output\x18\x01 \x01(\tR\x06output2\x93\x02\n" +
	"\rPluginService\x120\n" +
	"\aGetName\x12\r.shared.Empty\x1a\x16.shared.StringResponse\x123\n" +
	"\n" +
	"GetVersion\x12\r.shared.Empty\x1a\x16.shared.StringResponse\x126\n" +
	"\n" +
	"Initialize\x12\x19.shared.InitializeRequest\x1a\r.shared.Empty\x12:\n" +
	"\aExecute\x12\x16.shared.ExecuteRequest\x1a\x17.shared.ExecuteResponse\x12'\n" +
	"\aCleanup\x12\r.shared.Empty\x1a\r.shared.EmptyB\n" +
	"Z\b./sharedb\x06proto3"

var (
	file_plugin_proto_rawDescOnce sync.Once
	file_plugin_proto_rawDescData []byte
)

func file_plugin_proto_rawDescGZIP() []byte {
	file_plugin_proto_rawDescOnce.Do(func() {
		file_plugin_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_plugin_proto_rawDesc), len(file_plugin_proto_rawDesc)))
	})
	return file_plugin_proto_rawDescData
}

var file_plugin_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_plugin_proto_goTypes = []any{
	(*Empty)(nil),             // 0: shared.Empty
	(*StringResponse)(nil),    // 1: shared.StringResponse
	(*InitializeRequest)(nil), // 2: shared.InitializeRequest
	(*ExecuteRequest)(nil),    // 3: shared.ExecuteRequest
	(*ExecuteResponse)(nil),   // 4: shared.ExecuteResponse
	nil,                       // 5: shared.InitializeRequest.ConfigEntry
}
var file_plugin_proto_depIdxs = []int32{
	5, // 0: shared.InitializeRequest.config:type_name -> shared.InitializeRequest.ConfigEntry
	0, // 1: shared.PluginService.GetName:input_type -> shared.Empty
	0, // 2: shared.PluginService.GetVersion:input_type -> shared.Empty
	2, // 3: shared.PluginService.Initialize:input_type -> shared.InitializeRequest
	3, // 4: shared.PluginService.Execute:input_type -> shared.ExecuteRequest
	0, // 5: shared.PluginService.Cleanup:input_type -> shared.Empty
	1, // 6: shared.PluginService.GetName:output_type -> shared.StringResponse
	1, // 7: shared.PluginService.GetVersion:output_type -> shared.StringResponse
	0, // 8: shared.PluginService.Initialize:output_type -> shared.Empty
	4, // 9: shared.PluginService.Execute:output_type -> shared.ExecuteResponse
	0, // 10: shared.PluginService.Cleanup:output_type -> shared.Empty
	6, // [6:11] is the sub-list for method output_type
	1, // [1:6] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_plugin_proto_init() }
func file_plugin_proto_init() {
	if File_plugin_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_plugin_proto_rawDesc), len(file_plugin_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_plugin_proto_goTypes,
		DependencyIndexes: file_plugin_proto_depIdxs,
		MessageInfos:      file_plugin_proto_msgTypes,
	}.Build()
	File_plugin_proto = out.File
	file_plugin_proto_goTypes = nil
	file_plugin_proto_depIdxs = nil
}
