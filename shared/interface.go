package shared

import (
	"context"
)

// PluginInterface 定义插件必须实现的接口
type PluginInterface interface {
	// GetName 返回插件名称
	GetName() string
	
	// GetVersion 返回插件版本
	GetVersion() string
	
	// Initialize 初始化插件
	Initialize(config map[string]string) error
	
	// Execute 执行插件主要功能
	Execute(ctx context.Context, input string) (string, error)
	
	// Cleanup 清理插件资源
	Cleanup() error
}

// PluginInfo 插件信息结构
type PluginInfo struct {
	Name        string            `json:"name"`
	Version     string            `json:"version"`
	Description string            `json:"description"`
	Config      map[string]string `json:"config"`
}
