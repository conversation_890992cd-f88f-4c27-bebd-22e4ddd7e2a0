syntax = "proto3";

package shared;

option go_package = "./shared";

// 插件服务定义
service PluginService {
  rpc GetName(Empty) returns (StringResponse);
  rpc GetVersion(Empty) returns (StringResponse);
  rpc Initialize(InitializeRequest) returns (Empty);
  rpc Execute(ExecuteRequest) returns (ExecuteResponse);
  rpc Cleanup(Empty) returns (Empty);
}

// 空消息
message Empty {}

// 字符串响应
message StringResponse {
  string value = 1;
}

// 初始化请求
message InitializeRequest {
  map<string, string> config = 1;
}

// 执行请求
message ExecuteRequest {
  string input = 1;
}

// 执行响应
message ExecuteResponse {
  string output = 1;
}
