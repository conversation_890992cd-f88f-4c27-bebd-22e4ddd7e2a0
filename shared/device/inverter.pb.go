// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: inverter.proto

package device

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 表位配置
// common_type 通信类型， 默认 0 串口
// common_param 通讯参数, 串口：9600，8，N，1
// protocol 逆变器协议
type PositionConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	CommonType    int32                  `protobuf:"varint,2,opt,name=common_type,json=commonType,proto3" json:"common_type,omitempty"`
	CommonParam   string                 `protobuf:"bytes,3,opt,name=common_param,json=commonParam,proto3" json:"common_param,omitempty"`
	Protocol      string                 `protobuf:"bytes,4,opt,name=protocol,proto3" json:"protocol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PositionConfig) Reset() {
	*x = PositionConfig{}
	mi := &file_inverter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionConfig) ProtoMessage() {}

func (x *PositionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionConfig.ProtoReflect.Descriptor instead.
func (*PositionConfig) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{0}
}

func (x *PositionConfig) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *PositionConfig) GetCommonType() int32 {
	if x != nil {
		return x.CommonType
	}
	return 0
}

func (x *PositionConfig) GetCommonParam() string {
	if x != nil {
		return x.CommonParam
	}
	return ""
}

func (x *PositionConfig) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

// 寄存器地址及值
type Register struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Addr          int32                  `protobuf:"varint,1,opt,name=addr,proto3" json:"addr,omitempty"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Register) Reset() {
	*x = Register{}
	mi := &file_inverter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Register) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Register) ProtoMessage() {}

func (x *Register) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Register.ProtoReflect.Descriptor instead.
func (*Register) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{1}
}

func (x *Register) GetAddr() int32 {
	if x != nil {
		return x.Addr
	}
	return 0
}

func (x *Register) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 初始化逆变器请求
type InverterInitializeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Positions     *PositionConfig        `protobuf:"bytes,1,opt,name=positions,proto3" json:"positions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterInitializeRequest) Reset() {
	*x = InverterInitializeRequest{}
	mi := &file_inverter_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterInitializeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterInitializeRequest) ProtoMessage() {}

func (x *InverterInitializeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterInitializeRequest.ProtoReflect.Descriptor instead.
func (*InverterInitializeRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{2}
}

func (x *InverterInitializeRequest) GetPositions() *PositionConfig {
	if x != nil {
		return x.Positions
	}
	return nil
}

// 初始化逆变器响应
type InverterInitializeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterInitializeResponse) Reset() {
	*x = InverterInitializeResponse{}
	mi := &file_inverter_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterInitializeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterInitializeResponse) ProtoMessage() {}

func (x *InverterInitializeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterInitializeResponse.ProtoReflect.Descriptor instead.
func (*InverterInitializeResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{3}
}

func (x *InverterInitializeResponse) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

// 销毁逆变器请求
type InverterCleanupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterCleanupRequest) Reset() {
	*x = InverterCleanupRequest{}
	mi := &file_inverter_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterCleanupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterCleanupRequest) ProtoMessage() {}

func (x *InverterCleanupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterCleanupRequest.ProtoReflect.Descriptor instead.
func (*InverterCleanupRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{4}
}

func (x *InverterCleanupRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

// 销毁逆变器响应
type InverterCleanupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterCleanupResponse) Reset() {
	*x = InverterCleanupResponse{}
	mi := &file_inverter_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterCleanupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterCleanupResponse) ProtoMessage() {}

func (x *InverterCleanupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterCleanupResponse.ProtoReflect.Descriptor instead.
func (*InverterCleanupResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{5}
}

// 修改逆变器通讯参数请求
type InverterModifyCommParamRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	CommonType    int32                  `protobuf:"varint,2,opt,name=common_type,json=commonType,proto3" json:"common_type,omitempty"`
	CommonParam   string                 `protobuf:"bytes,3,opt,name=common_param,json=commonParam,proto3" json:"common_param,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterModifyCommParamRequest) Reset() {
	*x = InverterModifyCommParamRequest{}
	mi := &file_inverter_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterModifyCommParamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterModifyCommParamRequest) ProtoMessage() {}

func (x *InverterModifyCommParamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterModifyCommParamRequest.ProtoReflect.Descriptor instead.
func (*InverterModifyCommParamRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{6}
}

func (x *InverterModifyCommParamRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *InverterModifyCommParamRequest) GetCommonType() int32 {
	if x != nil {
		return x.CommonType
	}
	return 0
}

func (x *InverterModifyCommParamRequest) GetCommonParam() string {
	if x != nil {
		return x.CommonParam
	}
	return ""
}

// 修改逆变器通讯参数响应
type InverterModifyCommParamResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterModifyCommParamResponse) Reset() {
	*x = InverterModifyCommParamResponse{}
	mi := &file_inverter_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterModifyCommParamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterModifyCommParamResponse) ProtoMessage() {}

func (x *InverterModifyCommParamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterModifyCommParamResponse.ProtoReflect.Descriptor instead.
func (*InverterModifyCommParamResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{7}
}

// 修改逆变器协议
type InverterModifyProtocolRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	Protocol      string                 `protobuf:"bytes,2,opt,name=protocol,proto3" json:"protocol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterModifyProtocolRequest) Reset() {
	*x = InverterModifyProtocolRequest{}
	mi := &file_inverter_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterModifyProtocolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterModifyProtocolRequest) ProtoMessage() {}

func (x *InverterModifyProtocolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterModifyProtocolRequest.ProtoReflect.Descriptor instead.
func (*InverterModifyProtocolRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{8}
}

func (x *InverterModifyProtocolRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *InverterModifyProtocolRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

// 修改逆变器协议响应
type InverterModifyProtocolResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterModifyProtocolResponse) Reset() {
	*x = InverterModifyProtocolResponse{}
	mi := &file_inverter_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterModifyProtocolResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterModifyProtocolResponse) ProtoMessage() {}

func (x *InverterModifyProtocolResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterModifyProtocolResponse.ProtoReflect.Descriptor instead.
func (*InverterModifyProtocolResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{9}
}

// 获取逆变器数据请求
type InverterGetDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	ParamType     int32                  `protobuf:"varint,2,opt,name=param_type,json=paramType,proto3" json:"param_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterGetDataRequest) Reset() {
	*x = InverterGetDataRequest{}
	mi := &file_inverter_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterGetDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterGetDataRequest) ProtoMessage() {}

func (x *InverterGetDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterGetDataRequest.ProtoReflect.Descriptor instead.
func (*InverterGetDataRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{10}
}

func (x *InverterGetDataRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *InverterGetDataRequest) GetParamType() int32 {
	if x != nil {
		return x.ParamType
	}
	return 0
}

// 获取逆变器数据响应
type InverterGetDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	ParamType     int32                  `protobuf:"varint,2,opt,name=param_type,json=paramType,proto3" json:"param_type,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterGetDataResponse) Reset() {
	*x = InverterGetDataResponse{}
	mi := &file_inverter_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterGetDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterGetDataResponse) ProtoMessage() {}

func (x *InverterGetDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterGetDataResponse.ProtoReflect.Descriptor instead.
func (*InverterGetDataResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{11}
}

func (x *InverterGetDataResponse) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *InverterGetDataResponse) GetParamType() int32 {
	if x != nil {
		return x.ParamType
	}
	return 0
}

func (x *InverterGetDataResponse) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 设置逆变器数据请求
type InverterSetDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	ParamType     int32                  `protobuf:"varint,2,opt,name=param_type,json=paramType,proto3" json:"param_type,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterSetDataRequest) Reset() {
	*x = InverterSetDataRequest{}
	mi := &file_inverter_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterSetDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterSetDataRequest) ProtoMessage() {}

func (x *InverterSetDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterSetDataRequest.ProtoReflect.Descriptor instead.
func (*InverterSetDataRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{12}
}

func (x *InverterSetDataRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *InverterSetDataRequest) GetParamType() int32 {
	if x != nil {
		return x.ParamType
	}
	return 0
}

func (x *InverterSetDataRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 设置逆变器数据响应
type InverterSetDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InverterSetDataResponse) Reset() {
	*x = InverterSetDataResponse{}
	mi := &file_inverter_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InverterSetDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InverterSetDataResponse) ProtoMessage() {}

func (x *InverterSetDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InverterSetDataResponse.ProtoReflect.Descriptor instead.
func (*InverterSetDataResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{13}
}

var File_inverter_proto protoreflect.FileDescriptor

const file_inverter_proto_rawDesc = "" +
	"\n" +
	"\x0einverter.proto\x12\x06shared\"\x8c\x01\n" +
	"\x0ePositionConfig\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1f\n" +
	"\vcommon_type\x18\x02 \x01(\x05R\n" +
	"commonType\x12!\n" +
	"\fcommon_param\x18\x03 \x01(\tR\vcommonParam\x12\x1a\n" +
	"\bprotocol\x18\x04 \x01(\tR\bprotocol\"4\n" +
	"\bRegister\x12\x12\n" +
	"\x04addr\x18\x01 \x01(\x05R\x04addr\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value\"Q\n" +
	"\x19InverterInitializeRequest\x124\n" +
	"\tpositions\x18\x01 \x01(\v2\x16.shared.PositionConfigR\tpositions\"8\n" +
	"\x1aInverterInitializeResponse\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\"4\n" +
	"\x16InverterCleanupRequest\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\"\x19\n" +
	"\x17InverterCleanupResponse\"\x80\x01\n" +
	"\x1eInverterModifyCommParamRequest\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1f\n" +
	"\vcommon_type\x18\x02 \x01(\x05R\n" +
	"commonType\x12!\n" +
	"\fcommon_param\x18\x03 \x01(\tR\vcommonParam\"!\n" +
	"\x1fInverterModifyCommParamResponse\"W\n" +
	"\x1dInverterModifyProtocolRequest\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1a\n" +
	"\bprotocol\x18\x02 \x01(\tR\bprotocol\" \n" +
	"\x1eInverterModifyProtocolResponse\"S\n" +
	"\x16InverterGetDataRequest\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"param_type\x18\x02 \x01(\x05R\tparamType\"j\n" +
	"\x17InverterGetDataResponse\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"param_type\x18\x02 \x01(\x05R\tparamType\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\"i\n" +
	"\x16InverterSetDataRequest\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"param_type\x18\x02 \x01(\x05R\tparamType\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\"\x19\n" +
	"\x17InverterSetDataResponse2\x8f\x04\n" +
	"\x0fInverterService\x12S\n" +
	"\n" +
	"Initialize\x12!.shared.InverterInitializeRequest\x1a\".shared.InverterInitializeResponse\x12J\n" +
	"\aCleanup\x12\x1e.shared.InverterCleanupRequest\x1a\x1f.shared.InverterCleanupResponse\x12b\n" +
	"\x0fModifyCommParam\x12&.shared.InverterModifyCommParamRequest\x1a'.shared.InverterModifyCommParamResponse\x12_\n" +
	"\x0eModifyProtocol\x12%.shared.InverterModifyProtocolRequest\x1a&.shared.InverterModifyProtocolResponse\x12J\n" +
	"\aGetData\x12\x1e.shared.InverterGetDataRequest\x1a\x1f.shared.InverterGetDataResponse\x12J\n" +
	"\aSetData\x12\x1e.shared.InverterSetDataRequest\x1a\x1f.shared.InverterSetDataResponseB\n" +
	"Z\b./deviceb\x06proto3"

var (
	file_inverter_proto_rawDescOnce sync.Once
	file_inverter_proto_rawDescData []byte
)

func file_inverter_proto_rawDescGZIP() []byte {
	file_inverter_proto_rawDescOnce.Do(func() {
		file_inverter_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inverter_proto_rawDesc), len(file_inverter_proto_rawDesc)))
	})
	return file_inverter_proto_rawDescData
}

var file_inverter_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_inverter_proto_goTypes = []any{
	(*PositionConfig)(nil),                  // 0: shared.PositionConfig
	(*Register)(nil),                        // 1: shared.Register
	(*InverterInitializeRequest)(nil),       // 2: shared.InverterInitializeRequest
	(*InverterInitializeResponse)(nil),      // 3: shared.InverterInitializeResponse
	(*InverterCleanupRequest)(nil),          // 4: shared.InverterCleanupRequest
	(*InverterCleanupResponse)(nil),         // 5: shared.InverterCleanupResponse
	(*InverterModifyCommParamRequest)(nil),  // 6: shared.InverterModifyCommParamRequest
	(*InverterModifyCommParamResponse)(nil), // 7: shared.InverterModifyCommParamResponse
	(*InverterModifyProtocolRequest)(nil),   // 8: shared.InverterModifyProtocolRequest
	(*InverterModifyProtocolResponse)(nil),  // 9: shared.InverterModifyProtocolResponse
	(*InverterGetDataRequest)(nil),          // 10: shared.InverterGetDataRequest
	(*InverterGetDataResponse)(nil),         // 11: shared.InverterGetDataResponse
	(*InverterSetDataRequest)(nil),          // 12: shared.InverterSetDataRequest
	(*InverterSetDataResponse)(nil),         // 13: shared.InverterSetDataResponse
}
var file_inverter_proto_depIdxs = []int32{
	0,  // 0: shared.InverterInitializeRequest.positions:type_name -> shared.PositionConfig
	2,  // 1: shared.InverterService.Initialize:input_type -> shared.InverterInitializeRequest
	4,  // 2: shared.InverterService.Cleanup:input_type -> shared.InverterCleanupRequest
	6,  // 3: shared.InverterService.ModifyCommParam:input_type -> shared.InverterModifyCommParamRequest
	8,  // 4: shared.InverterService.ModifyProtocol:input_type -> shared.InverterModifyProtocolRequest
	10, // 5: shared.InverterService.GetData:input_type -> shared.InverterGetDataRequest
	12, // 6: shared.InverterService.SetData:input_type -> shared.InverterSetDataRequest
	3,  // 7: shared.InverterService.Initialize:output_type -> shared.InverterInitializeResponse
	5,  // 8: shared.InverterService.Cleanup:output_type -> shared.InverterCleanupResponse
	7,  // 9: shared.InverterService.ModifyCommParam:output_type -> shared.InverterModifyCommParamResponse
	9,  // 10: shared.InverterService.ModifyProtocol:output_type -> shared.InverterModifyProtocolResponse
	11, // 11: shared.InverterService.GetData:output_type -> shared.InverterGetDataResponse
	13, // 12: shared.InverterService.SetData:output_type -> shared.InverterSetDataResponse
	7,  // [7:13] is the sub-list for method output_type
	1,  // [1:7] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_inverter_proto_init() }
func file_inverter_proto_init() {
	if File_inverter_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inverter_proto_rawDesc), len(file_inverter_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inverter_proto_goTypes,
		DependencyIndexes: file_inverter_proto_depIdxs,
		MessageInfos:      file_inverter_proto_msgTypes,
	}.Build()
	File_inverter_proto = out.File
	file_inverter_proto_goTypes = nil
	file_inverter_proto_depIdxs = nil
}
