// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: inverter.proto

package device

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 表位配置
// common_type 通信类型， 默认 0 串口
// common_param 通讯参数, 串口：9600，8，N，1
// protocol 逆变器协议
type PositionConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	CommonType    int32                  `protobuf:"varint,2,opt,name=common_type,json=commonType,proto3" json:"common_type,omitempty"`
	CommonParam   string                 `protobuf:"bytes,3,opt,name=common_param,json=commonParam,proto3" json:"common_param,omitempty"`
	Protocol      string                 `protobuf:"bytes,4,opt,name=protocol,proto3" json:"protocol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PositionConfig) Reset() {
	*x = PositionConfig{}
	mi := &file_inverter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionConfig) ProtoMessage() {}

func (x *PositionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionConfig.ProtoReflect.Descriptor instead.
func (*PositionConfig) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{0}
}

func (x *PositionConfig) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *PositionConfig) GetCommonType() int32 {
	if x != nil {
		return x.CommonType
	}
	return 0
}

func (x *PositionConfig) GetCommonParam() string {
	if x != nil {
		return x.CommonParam
	}
	return ""
}

func (x *PositionConfig) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

// 寄存器地址及值
type Register struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Addr          int32                  `protobuf:"varint,1,opt,name=addr,proto3" json:"addr,omitempty"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Register) Reset() {
	*x = Register{}
	mi := &file_inverter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Register) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Register) ProtoMessage() {}

func (x *Register) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Register.ProtoReflect.Descriptor instead.
func (*Register) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{1}
}

func (x *Register) GetAddr() int32 {
	if x != nil {
		return x.Addr
	}
	return 0
}

func (x *Register) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 初始化逆变器请求
type InitializeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Positions     *PositionConfig        `protobuf:"bytes,1,opt,name=positions,proto3" json:"positions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitializeRequest) Reset() {
	*x = InitializeRequest{}
	mi := &file_inverter_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitializeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitializeRequest) ProtoMessage() {}

func (x *InitializeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitializeRequest.ProtoReflect.Descriptor instead.
func (*InitializeRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{2}
}

func (x *InitializeRequest) GetPositions() *PositionConfig {
	if x != nil {
		return x.Positions
	}
	return nil
}

// 初始化逆变器响应
type InitializeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitializeResponse) Reset() {
	*x = InitializeResponse{}
	mi := &file_inverter_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitializeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitializeResponse) ProtoMessage() {}

func (x *InitializeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitializeResponse.ProtoReflect.Descriptor instead.
func (*InitializeResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{3}
}

func (x *InitializeResponse) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

// 销毁逆变器请求
type CleanupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanupRequest) Reset() {
	*x = CleanupRequest{}
	mi := &file_inverter_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupRequest) ProtoMessage() {}

func (x *CleanupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupRequest.ProtoReflect.Descriptor instead.
func (*CleanupRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{4}
}

func (x *CleanupRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

// 销毁逆变器响应
type CleanupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanupResponse) Reset() {
	*x = CleanupResponse{}
	mi := &file_inverter_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupResponse) ProtoMessage() {}

func (x *CleanupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupResponse.ProtoReflect.Descriptor instead.
func (*CleanupResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{5}
}

// 修改逆变器通讯参数请求
type ModifyCommParamRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	CommonType    int32                  `protobuf:"varint,2,opt,name=common_type,json=commonType,proto3" json:"common_type,omitempty"`
	CommonParam   string                 `protobuf:"bytes,3,opt,name=common_param,json=commonParam,proto3" json:"common_param,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModifyCommParamRequest) Reset() {
	*x = ModifyCommParamRequest{}
	mi := &file_inverter_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModifyCommParamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyCommParamRequest) ProtoMessage() {}

func (x *ModifyCommParamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyCommParamRequest.ProtoReflect.Descriptor instead.
func (*ModifyCommParamRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{6}
}

func (x *ModifyCommParamRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *ModifyCommParamRequest) GetCommonType() int32 {
	if x != nil {
		return x.CommonType
	}
	return 0
}

func (x *ModifyCommParamRequest) GetCommonParam() string {
	if x != nil {
		return x.CommonParam
	}
	return ""
}

// 修改逆变器通讯参数响应
type ModifyCommParamResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModifyCommParamResponse) Reset() {
	*x = ModifyCommParamResponse{}
	mi := &file_inverter_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModifyCommParamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyCommParamResponse) ProtoMessage() {}

func (x *ModifyCommParamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyCommParamResponse.ProtoReflect.Descriptor instead.
func (*ModifyCommParamResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{7}
}

// 修改逆变器协议
type ModifyProtocolRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	Protocol      string                 `protobuf:"bytes,2,opt,name=protocol,proto3" json:"protocol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModifyProtocolRequest) Reset() {
	*x = ModifyProtocolRequest{}
	mi := &file_inverter_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModifyProtocolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyProtocolRequest) ProtoMessage() {}

func (x *ModifyProtocolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyProtocolRequest.ProtoReflect.Descriptor instead.
func (*ModifyProtocolRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{8}
}

func (x *ModifyProtocolRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *ModifyProtocolRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

// 修改逆变器协议响应
type ModifyProtocolResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModifyProtocolResponse) Reset() {
	*x = ModifyProtocolResponse{}
	mi := &file_inverter_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModifyProtocolResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyProtocolResponse) ProtoMessage() {}

func (x *ModifyProtocolResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyProtocolResponse.ProtoReflect.Descriptor instead.
func (*ModifyProtocolResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{9}
}

// 获取逆变器数据请求
type GetDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	ParamType     int32                  `protobuf:"varint,2,opt,name=param_type,json=paramType,proto3" json:"param_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDataRequest) Reset() {
	*x = GetDataRequest{}
	mi := &file_inverter_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataRequest) ProtoMessage() {}

func (x *GetDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataRequest.ProtoReflect.Descriptor instead.
func (*GetDataRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{10}
}

func (x *GetDataRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *GetDataRequest) GetParamType() int32 {
	if x != nil {
		return x.ParamType
	}
	return 0
}

// 获取逆变器数据响应
type GetDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	ParamType     int32                  `protobuf:"varint,2,opt,name=param_type,json=paramType,proto3" json:"param_type,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDataResponse) Reset() {
	*x = GetDataResponse{}
	mi := &file_inverter_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataResponse) ProtoMessage() {}

func (x *GetDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataResponse.ProtoReflect.Descriptor instead.
func (*GetDataResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{11}
}

func (x *GetDataResponse) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *GetDataResponse) GetParamType() int32 {
	if x != nil {
		return x.ParamType
	}
	return 0
}

func (x *GetDataResponse) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 设置逆变器数据请求
type SetDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Position      int32                  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	ParamType     int32                  `protobuf:"varint,2,opt,name=param_type,json=paramType,proto3" json:"param_type,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetDataRequest) Reset() {
	*x = SetDataRequest{}
	mi := &file_inverter_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDataRequest) ProtoMessage() {}

func (x *SetDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDataRequest.ProtoReflect.Descriptor instead.
func (*SetDataRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{12}
}

func (x *SetDataRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *SetDataRequest) GetParamType() int32 {
	if x != nil {
		return x.ParamType
	}
	return 0
}

func (x *SetDataRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 设置逆变器数据响应
type SetDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetDataResponse) Reset() {
	*x = SetDataResponse{}
	mi := &file_inverter_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDataResponse) ProtoMessage() {}

func (x *SetDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDataResponse.ProtoReflect.Descriptor instead.
func (*SetDataResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{13}
}

// 空消息
type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_inverter_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{14}
}

// 字符串响应
type StringResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StringResponse) Reset() {
	*x = StringResponse{}
	mi := &file_inverter_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringResponse) ProtoMessage() {}

func (x *StringResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringResponse.ProtoReflect.Descriptor instead.
func (*StringResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{15}
}

func (x *StringResponse) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 执行请求
type ExecuteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Input         string                 `protobuf:"bytes,1,opt,name=input,proto3" json:"input,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteRequest) Reset() {
	*x = ExecuteRequest{}
	mi := &file_inverter_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteRequest) ProtoMessage() {}

func (x *ExecuteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteRequest.ProtoReflect.Descriptor instead.
func (*ExecuteRequest) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{16}
}

func (x *ExecuteRequest) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

// 执行响应
type ExecuteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Output        string                 `protobuf:"bytes,1,opt,name=output,proto3" json:"output,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteResponse) Reset() {
	*x = ExecuteResponse{}
	mi := &file_inverter_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteResponse) ProtoMessage() {}

func (x *ExecuteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inverter_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteResponse.ProtoReflect.Descriptor instead.
func (*ExecuteResponse) Descriptor() ([]byte, []int) {
	return file_inverter_proto_rawDescGZIP(), []int{17}
}

func (x *ExecuteResponse) GetOutput() string {
	if x != nil {
		return x.Output
	}
	return ""
}

var File_inverter_proto protoreflect.FileDescriptor

const file_inverter_proto_rawDesc = "" +
	"\n" +
	"\x0einverter.proto\x12\x06shared\"\x8c\x01\n" +
	"\x0ePositionConfig\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1f\n" +
	"\vcommon_type\x18\x02 \x01(\x05R\n" +
	"commonType\x12!\n" +
	"\fcommon_param\x18\x03 \x01(\tR\vcommonParam\x12\x1a\n" +
	"\bprotocol\x18\x04 \x01(\tR\bprotocol\"4\n" +
	"\bRegister\x12\x12\n" +
	"\x04addr\x18\x01 \x01(\x05R\x04addr\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value\"I\n" +
	"\x11InitializeRequest\x124\n" +
	"\tpositions\x18\x01 \x01(\v2\x16.shared.PositionConfigR\tpositions\"0\n" +
	"\x12InitializeResponse\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\",\n" +
	"\x0eCleanupRequest\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\"\x11\n" +
	"\x0fCleanupResponse\"x\n" +
	"\x16ModifyCommParamRequest\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1f\n" +
	"\vcommon_type\x18\x02 \x01(\x05R\n" +
	"commonType\x12!\n" +
	"\fcommon_param\x18\x03 \x01(\tR\vcommonParam\"\x19\n" +
	"\x17ModifyCommParamResponse\"O\n" +
	"\x15ModifyProtocolRequest\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1a\n" +
	"\bprotocol\x18\x02 \x01(\tR\bprotocol\"\x18\n" +
	"\x16ModifyProtocolResponse\"K\n" +
	"\x0eGetDataRequest\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"param_type\x18\x02 \x01(\x05R\tparamType\"b\n" +
	"\x0fGetDataResponse\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"param_type\x18\x02 \x01(\x05R\tparamType\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\"a\n" +
	"\x0eSetDataRequest\x12\x1a\n" +
	"\bposition\x18\x01 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"param_type\x18\x02 \x01(\x05R\tparamType\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\"\x11\n" +
	"\x0fSetDataResponse\"\a\n" +
	"\x05Empty\"&\n" +
	"\x0eStringResponse\x12\x14\n" +
	"\x05value\x18\x01 \x01(\tR\x05value\"&\n" +
	"\x0eExecuteRequest\x12\x14\n" +
	"\x05input\x18\x01 \x01(\tR\x05input\")\n" +
	"\x0fExecuteResponse\x12\x16\n" +
	"\x06output\x18\x01 \x01(\tR\x06output2\xe9\x03\n" +
	"\rPluginService\x12C\n" +
	"\n" +
	"Initialize\x12\x19.shared.InitializeRequest\x1a\x1a.shared.InitializeResponse\x12:\n" +
	"\aCleanup\x12\x16.shared.CleanupRequest\x1a\x17.shared.CleanupResponse\x12R\n" +
	"\x0fModifyCommParam\x12\x1e.shared.ModifyCommParamRequest\x1a\x1f.shared.ModifyCommParamResponse\x12O\n" +
	"\x0eModifyProtocol\x12\x1d.shared.ModifyProtocolRequest\x1a\x1e.shared.ModifyProtocolResponse\x12:\n" +
	"\aGetData\x12\x16.shared.GetDataRequest\x1a\x17.shared.GetDataResponse\x12:\n" +
	"\aSetData\x12\x16.shared.SetDataRequest\x1a\x17.shared.SetDataResponse\x12:\n" +
	"\aExecute\x12\x16.shared.ExecuteRequest\x1a\x17.shared.ExecuteResponseB\n" +
	"Z\b./deviceb\x06proto3"

var (
	file_inverter_proto_rawDescOnce sync.Once
	file_inverter_proto_rawDescData []byte
)

func file_inverter_proto_rawDescGZIP() []byte {
	file_inverter_proto_rawDescOnce.Do(func() {
		file_inverter_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inverter_proto_rawDesc), len(file_inverter_proto_rawDesc)))
	})
	return file_inverter_proto_rawDescData
}

var file_inverter_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_inverter_proto_goTypes = []any{
	(*PositionConfig)(nil),          // 0: shared.PositionConfig
	(*Register)(nil),                // 1: shared.Register
	(*InitializeRequest)(nil),       // 2: shared.InitializeRequest
	(*InitializeResponse)(nil),      // 3: shared.InitializeResponse
	(*CleanupRequest)(nil),          // 4: shared.CleanupRequest
	(*CleanupResponse)(nil),         // 5: shared.CleanupResponse
	(*ModifyCommParamRequest)(nil),  // 6: shared.ModifyCommParamRequest
	(*ModifyCommParamResponse)(nil), // 7: shared.ModifyCommParamResponse
	(*ModifyProtocolRequest)(nil),   // 8: shared.ModifyProtocolRequest
	(*ModifyProtocolResponse)(nil),  // 9: shared.ModifyProtocolResponse
	(*GetDataRequest)(nil),          // 10: shared.GetDataRequest
	(*GetDataResponse)(nil),         // 11: shared.GetDataResponse
	(*SetDataRequest)(nil),          // 12: shared.SetDataRequest
	(*SetDataResponse)(nil),         // 13: shared.SetDataResponse
	(*Empty)(nil),                   // 14: shared.Empty
	(*StringResponse)(nil),          // 15: shared.StringResponse
	(*ExecuteRequest)(nil),          // 16: shared.ExecuteRequest
	(*ExecuteResponse)(nil),         // 17: shared.ExecuteResponse
}
var file_inverter_proto_depIdxs = []int32{
	0,  // 0: shared.InitializeRequest.positions:type_name -> shared.PositionConfig
	2,  // 1: shared.PluginService.Initialize:input_type -> shared.InitializeRequest
	4,  // 2: shared.PluginService.Cleanup:input_type -> shared.CleanupRequest
	6,  // 3: shared.PluginService.ModifyCommParam:input_type -> shared.ModifyCommParamRequest
	8,  // 4: shared.PluginService.ModifyProtocol:input_type -> shared.ModifyProtocolRequest
	10, // 5: shared.PluginService.GetData:input_type -> shared.GetDataRequest
	12, // 6: shared.PluginService.SetData:input_type -> shared.SetDataRequest
	16, // 7: shared.PluginService.Execute:input_type -> shared.ExecuteRequest
	3,  // 8: shared.PluginService.Initialize:output_type -> shared.InitializeResponse
	5,  // 9: shared.PluginService.Cleanup:output_type -> shared.CleanupResponse
	7,  // 10: shared.PluginService.ModifyCommParam:output_type -> shared.ModifyCommParamResponse
	9,  // 11: shared.PluginService.ModifyProtocol:output_type -> shared.ModifyProtocolResponse
	11, // 12: shared.PluginService.GetData:output_type -> shared.GetDataResponse
	13, // 13: shared.PluginService.SetData:output_type -> shared.SetDataResponse
	17, // 14: shared.PluginService.Execute:output_type -> shared.ExecuteResponse
	8,  // [8:15] is the sub-list for method output_type
	1,  // [1:8] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_inverter_proto_init() }
func file_inverter_proto_init() {
	if File_inverter_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inverter_proto_rawDesc), len(file_inverter_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inverter_proto_goTypes,
		DependencyIndexes: file_inverter_proto_depIdxs,
		MessageInfos:      file_inverter_proto_msgTypes,
	}.Build()
	File_inverter_proto = out.File
	file_inverter_proto_goTypes = nil
	file_inverter_proto_depIdxs = nil
}
