// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: inverter.proto

package device

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	InverterService_Initialize_FullMethodName      = "/shared.InverterService/Initialize"
	InverterService_Cleanup_FullMethodName         = "/shared.InverterService/Cleanup"
	InverterService_ModifyCommParam_FullMethodName = "/shared.InverterService/ModifyCommParam"
	InverterService_ModifyProtocol_FullMethodName  = "/shared.InverterService/ModifyProtocol"
	InverterService_GetData_FullMethodName         = "/shared.InverterService/GetData"
	InverterService_SetData_FullMethodName         = "/shared.InverterService/SetData"
)

// InverterServiceClient is the client API for InverterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 逆变器设备服务定义
type InverterServiceClient interface {
	Initialize(ctx context.Context, in *InverterInitializeRequest, opts ...grpc.CallOption) (*InverterInitializeResponse, error)
	Cleanup(ctx context.Context, in *InverterCleanupRequest, opts ...grpc.CallOption) (*InverterCleanupResponse, error)
	ModifyCommParam(ctx context.Context, in *InverterModifyCommParamRequest, opts ...grpc.CallOption) (*InverterModifyCommParamResponse, error)
	ModifyProtocol(ctx context.Context, in *InverterModifyProtocolRequest, opts ...grpc.CallOption) (*InverterModifyProtocolResponse, error)
	GetData(ctx context.Context, in *InverterGetDataRequest, opts ...grpc.CallOption) (*InverterGetDataResponse, error)
	SetData(ctx context.Context, in *InverterSetDataRequest, opts ...grpc.CallOption) (*InverterSetDataResponse, error)
}

type inverterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewInverterServiceClient(cc grpc.ClientConnInterface) InverterServiceClient {
	return &inverterServiceClient{cc}
}

func (c *inverterServiceClient) Initialize(ctx context.Context, in *InverterInitializeRequest, opts ...grpc.CallOption) (*InverterInitializeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InverterInitializeResponse)
	err := c.cc.Invoke(ctx, InverterService_Initialize_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inverterServiceClient) Cleanup(ctx context.Context, in *InverterCleanupRequest, opts ...grpc.CallOption) (*InverterCleanupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InverterCleanupResponse)
	err := c.cc.Invoke(ctx, InverterService_Cleanup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inverterServiceClient) ModifyCommParam(ctx context.Context, in *InverterModifyCommParamRequest, opts ...grpc.CallOption) (*InverterModifyCommParamResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InverterModifyCommParamResponse)
	err := c.cc.Invoke(ctx, InverterService_ModifyCommParam_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inverterServiceClient) ModifyProtocol(ctx context.Context, in *InverterModifyProtocolRequest, opts ...grpc.CallOption) (*InverterModifyProtocolResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InverterModifyProtocolResponse)
	err := c.cc.Invoke(ctx, InverterService_ModifyProtocol_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inverterServiceClient) GetData(ctx context.Context, in *InverterGetDataRequest, opts ...grpc.CallOption) (*InverterGetDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InverterGetDataResponse)
	err := c.cc.Invoke(ctx, InverterService_GetData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inverterServiceClient) SetData(ctx context.Context, in *InverterSetDataRequest, opts ...grpc.CallOption) (*InverterSetDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InverterSetDataResponse)
	err := c.cc.Invoke(ctx, InverterService_SetData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InverterServiceServer is the server API for InverterService service.
// All implementations should embed UnimplementedInverterServiceServer
// for forward compatibility.
//
// 逆变器设备服务定义
type InverterServiceServer interface {
	Initialize(context.Context, *InverterInitializeRequest) (*InverterInitializeResponse, error)
	Cleanup(context.Context, *InverterCleanupRequest) (*InverterCleanupResponse, error)
	ModifyCommParam(context.Context, *InverterModifyCommParamRequest) (*InverterModifyCommParamResponse, error)
	ModifyProtocol(context.Context, *InverterModifyProtocolRequest) (*InverterModifyProtocolResponse, error)
	GetData(context.Context, *InverterGetDataRequest) (*InverterGetDataResponse, error)
	SetData(context.Context, *InverterSetDataRequest) (*InverterSetDataResponse, error)
}

// UnimplementedInverterServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedInverterServiceServer struct{}

func (UnimplementedInverterServiceServer) Initialize(context.Context, *InverterInitializeRequest) (*InverterInitializeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Initialize not implemented")
}
func (UnimplementedInverterServiceServer) Cleanup(context.Context, *InverterCleanupRequest) (*InverterCleanupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cleanup not implemented")
}
func (UnimplementedInverterServiceServer) ModifyCommParam(context.Context, *InverterModifyCommParamRequest) (*InverterModifyCommParamResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyCommParam not implemented")
}
func (UnimplementedInverterServiceServer) ModifyProtocol(context.Context, *InverterModifyProtocolRequest) (*InverterModifyProtocolResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyProtocol not implemented")
}
func (UnimplementedInverterServiceServer) GetData(context.Context, *InverterGetDataRequest) (*InverterGetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetData not implemented")
}
func (UnimplementedInverterServiceServer) SetData(context.Context, *InverterSetDataRequest) (*InverterSetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetData not implemented")
}
func (UnimplementedInverterServiceServer) testEmbeddedByValue() {}

// UnsafeInverterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InverterServiceServer will
// result in compilation errors.
type UnsafeInverterServiceServer interface {
	mustEmbedUnimplementedInverterServiceServer()
}

func RegisterInverterServiceServer(s grpc.ServiceRegistrar, srv InverterServiceServer) {
	// If the following call pancis, it indicates UnimplementedInverterServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&InverterService_ServiceDesc, srv)
}

func _InverterService_Initialize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InverterInitializeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InverterServiceServer).Initialize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InverterService_Initialize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InverterServiceServer).Initialize(ctx, req.(*InverterInitializeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InverterService_Cleanup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InverterCleanupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InverterServiceServer).Cleanup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InverterService_Cleanup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InverterServiceServer).Cleanup(ctx, req.(*InverterCleanupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InverterService_ModifyCommParam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InverterModifyCommParamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InverterServiceServer).ModifyCommParam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InverterService_ModifyCommParam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InverterServiceServer).ModifyCommParam(ctx, req.(*InverterModifyCommParamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InverterService_ModifyProtocol_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InverterModifyProtocolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InverterServiceServer).ModifyProtocol(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InverterService_ModifyProtocol_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InverterServiceServer).ModifyProtocol(ctx, req.(*InverterModifyProtocolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InverterService_GetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InverterGetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InverterServiceServer).GetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InverterService_GetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InverterServiceServer).GetData(ctx, req.(*InverterGetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InverterService_SetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InverterSetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InverterServiceServer).SetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InverterService_SetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InverterServiceServer).SetData(ctx, req.(*InverterSetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// InverterService_ServiceDesc is the grpc.ServiceDesc for InverterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var InverterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "shared.InverterService",
	HandlerType: (*InverterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Initialize",
			Handler:    _InverterService_Initialize_Handler,
		},
		{
			MethodName: "Cleanup",
			Handler:    _InverterService_Cleanup_Handler,
		},
		{
			MethodName: "ModifyCommParam",
			Handler:    _InverterService_ModifyCommParam_Handler,
		},
		{
			MethodName: "ModifyProtocol",
			Handler:    _InverterService_ModifyProtocol_Handler,
		},
		{
			MethodName: "GetData",
			Handler:    _InverterService_GetData_Handler,
		},
		{
			MethodName: "SetData",
			Handler:    _InverterService_SetData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inverter.proto",
}
