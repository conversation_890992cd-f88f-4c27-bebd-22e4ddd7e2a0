syntax = "proto3";

package shared;

option go_package = "./device";

// 逆变器设备服务定义
service InverterService {
    rpc Initialize(InverterInitializeRequest) returns (InverterInitializeResponse);
    rpc Cleanup(InverterCleanupRequest) returns (InverterCleanupResponse);
    rpc ModifyCommParam(InverterModifyCommParamRequest) returns (InverterModifyCommParamResponse);
    rpc ModifyProtocol(InverterModifyProtocolRequest) returns (InverterModifyProtocolResponse);
    rpc GetData(InverterGetDataRequest) returns (InverterGetDataResponse);
    rpc SetData(InverterSetDataRequest) returns (InverterSetDataResponse);
}

// 表位配置
// common_type 通信类型， 默认 0 串口
// common_param 通讯参数, 串口：9600，8，N，1 
// protocol 逆变器协议
message PositionConfig {
  int32 position = 1;
  int32 common_type = 2;
  string common_param = 3;
  string protocol = 4;
}

// 寄存器地址及值
message Register {
  int32 addr = 1;
  string value = 2;
}

// 初始化逆变器请求
message InverterInitializeRequest {
  PositionConfig positions = 1;
}

// 初始化逆变器响应
message InverterInitializeResponse {
  int32 position = 1;
}

// 销毁逆变器请求
message InverterCleanupRequest {
  int32 position = 1;
}

// 销毁逆变器响应
message InverterCleanupResponse {}

// 修改逆变器通讯参数请求
message InverterModifyCommParamRequest {
  int32 position = 1;
  int32 common_type = 2;
  string common_param = 3;
}

// 修改逆变器通讯参数响应
message InverterModifyCommParamResponse {}

// 修改逆变器协议
message InverterModifyProtocolRequest {
  int32 position = 1;
  string protocol = 2;
}

// 修改逆变器协议响应
message InverterModifyProtocolResponse {}

// 获取逆变器数据请求
message InverterGetDataRequest {
  int32 position = 1;
  int32 param_type = 2;
}

// 获取逆变器数据响应
message InverterGetDataResponse {
  int32 position = 1;
  int32 param_type = 2;
  string value = 3;
}

// 设置逆变器数据请求
message InverterSetDataRequest {
  int32 position = 1;
  int32 param_type = 2;
  string value = 3;
}

// 设置逆变器数据响应
message InverterSetDataResponse {}