syntax = "proto3";

package shared;

option go_package = "./device";

// 逆变器设备服务定义
service PluginService {
    rpc Initialize(InitializeRequest) returns (InitializeResponse);
    rpc Cleanup(CleanupRequest) returns (CleanupResponse);
    rpc ModifyCommParam(ModifyCommParamRequest) returns (ModifyCommParamResponse);
    rpc ModifyProtocol(ModifyProtocolRequest) returns (ModifyProtocolResponse);
    rpc GetData(GetDataRequest) returns (GetDataResponse);
    rpc SetData(SetDataRequest) returns (SetDataResponse);
    rpc Execute(ExecuteRequest) returns (ExecuteResponse);
}

// 表位配置
// common_type 通信类型， 默认 0 串口
// common_param 通讯参数, 串口：9600，8，N，1 
// protocol 逆变器协议
message PositionConfig {
  int32 position = 1;
  int32 common_type = 2;
  string common_param = 3;
  string protocol = 4;
}

// 寄存器地址及值
message Register {
  int32 addr = 1;
  string value = 2;
}

// 初始化逆变器请求
message InitializeRequest {
  PositionConfig positions = 1;
}

// 初始化逆变器响应
message InitializeResponse {
  int32 position = 1;
}

// 销毁逆变器请求
message CleanupRequest {
  int32 position = 1;
}

// 销毁逆变器响应
message CleanupResponse {}

// 修改逆变器通讯参数请求
message ModifyCommParamRequest {
  int32 position = 1;
  int32 common_type = 2;
  string common_param = 3;
}

// 修改逆变器通讯参数响应
message ModifyCommParamResponse {}

// 修改逆变器协议
message ModifyProtocolRequest {
  int32 position = 1;
  string protocol = 2;
}

// 修改逆变器协议响应
message ModifyProtocolResponse {}

// 获取逆变器数据请求
message GetDataRequest {
  int32 position = 1;
  int32 param_type = 2;
}

// 获取逆变器数据响应
message GetDataResponse {
  int32 position = 1;
  int32 param_type = 2;
  string value = 3;
}

// 设置逆变器数据请求
message SetDataRequest {
  int32 position = 1;
  int32 param_type = 2;
  string value = 3;
}

// 设置逆变器数据响应
message SetDataResponse {}

// 空消息
message Empty {}

// 字符串响应
message StringResponse {
  string value = 1;
}

// 执行请求
message ExecuteRequest {
  string input = 1;
}

// 执行响应
message ExecuteResponse {
  string output = 1;
}