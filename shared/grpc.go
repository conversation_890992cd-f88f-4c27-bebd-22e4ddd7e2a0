package shared

import (
	"context"

	"github.com/hashicorp/go-plugin"
	"google.golang.org/grpc"
)

// PluginName 插件名称常量
const PluginName = "plugin_interface"

// Handshake 是用于验证客户端和服务器之间的握手配置
var Handshake = plugin.HandshakeConfig{
	ProtocolVersion:  1,
	MagicCookieKey:   "PLUGIN_MAGIC_COOKIE",
	MagicCookieValue: "hello-world-plugin",
}

// PluginMap 是插件映射
var PluginMap = map[string]plugin.Plugin{
	PluginName: &PluginGRPC{},
}

// PluginGRPC 实现 plugin.GRPCPlugin 接口
type PluginGRPC struct {
	plugin.Plugin
	Impl PluginInterface
}

func (p *PluginGRPC) GRPCServer(broker *plugin.GRPCBroker, s *grpc.Server) error {
	RegisterPluginServiceServer(s, &GRPCServer{Impl: p.Impl})
	return nil
}

func (p *PluginGRPC) GRPCClient(ctx context.Context, broker *plugin.GRPCBroker, c *grpc.ClientConn) (interface{}, error) {
	return &GRPCClient{client: NewPluginServiceClient(c)}, nil
}

// GRPCClient 是客户端实现
type GRPCClient struct {
	client PluginServiceClient
}

func (m *GRPCClient) GetName() string {
	resp, err := m.client.GetName(context.Background(), &Empty{})
	if err != nil {
		return ""
	}
	return resp.Value
}

func (m *GRPCClient) GetVersion() string {
	resp, err := m.client.GetVersion(context.Background(), &Empty{})
	if err != nil {
		return ""
	}
	return resp.Value
}

func (m *GRPCClient) Initialize(config map[string]string) error {
	_, err := m.client.Initialize(context.Background(), &InitializeRequest{Config: config})
	return err
}

func (m *GRPCClient) Execute(ctx context.Context, input string) (string, error) {
	resp, err := m.client.Execute(ctx, &ExecuteRequest{Input: input})
	if err != nil {
		return "", err
	}
	return resp.Output, nil
}

func (m *GRPCClient) Cleanup() error {
	_, err := m.client.Cleanup(context.Background(), &Empty{})
	return err
}

// GRPCServer 是服务器实现
type GRPCServer struct {
	Impl PluginInterface
	UnimplementedPluginServiceServer
}

func (m *GRPCServer) GetName(ctx context.Context, req *Empty) (*StringResponse, error) {
	return &StringResponse{Value: m.Impl.GetName()}, nil
}

func (m *GRPCServer) GetVersion(ctx context.Context, req *Empty) (*StringResponse, error) {
	return &StringResponse{Value: m.Impl.GetVersion()}, nil
}

func (m *GRPCServer) Initialize(ctx context.Context, req *InitializeRequest) (*Empty, error) {
	err := m.Impl.Initialize(req.Config)
	return &Empty{}, err
}

func (m *GRPCServer) Execute(ctx context.Context, req *ExecuteRequest) (*ExecuteResponse, error) {
	output, err := m.Impl.Execute(ctx, req.Input)
	if err != nil {
		return nil, err
	}
	return &ExecuteResponse{Output: output}, nil
}

func (m *GRPCServer) Cleanup(ctx context.Context, req *Empty) (*Empty, error) {
	err := m.Impl.Cleanup()
	return &Empty{}, err
}
