# Go Plugin System

基于 go-plugin 构建的动态插件系统，支持通过 gRPC 进行进程间通信，可将插件编译成 .so 或 .dll 文件，由宿主进程在运行时动态加载。

## 特性

- 🔌 **动态插件加载**: 支持运行时加载和卸载插件
- 🌐 **gRPC 通信**: 插件与宿主进程通过 gRPC 协议通信
- 🔄 **跨平台支持**: 支持 Linux (.so) 和 Windows (.dll) 插件
- 🛡️ **进程隔离**: 每个插件运行在独立进程中，提高稳定性
- 📦 **简单接口**: 统一的插件接口，易于开发和集成
- 🎛️ **交互式管理**: 提供命令行界面管理插件

## 项目结构

```
.
├── shared/           # 共享接口和协议定义
│   ├── interface.go  # 插件接口定义
│   ├── grpc.go      # gRPC 实现
│   └── plugin.proto # Protocol Buffers 定义
├── host/            # 宿主进程
│   ├── main.go      # 主程序
│   └── plugin_manager.go # 插件管理器
├── plugins/         # 插件实现
│   ├── example/     # 示例插件
│   └── calculator/  # 计算器插件
├── build/           # 构建输出目录
├── Makefile         # 构建脚本
└── README.md        # 项目文档
```

## 快速开始

### 1. 环境准备

确保已安装以下工具：
- Go 1.19+
- Protocol Buffers 编译器 (protoc)
- Make

#### 安装 protoc

**Ubuntu/Debian:**
```bash
sudo apt-get install protobuf-compiler
```

**macOS:**
```bash
brew install protobuf
```

**Windows:**
从 [Protocol Buffers releases](https://github.com/protocolbuffers/protobuf/releases) 下载并安装

### 2. 构建项目

```bash
# 安装依赖和设置开发环境
make dev-setup

# 构建所有组件
make build

# 或者分步构建
make host      # 构建宿主程序
make plugins   # 构建所有插件
```

### 3. 运行系统

```bash
# 启动宿主程序（会自动加载 plugins 目录中的插件）
make run

# 或者手动运行
cd build/host
./plugin-host ../plugins
```

### 4. 使用交互式界面

启动后，您将看到交互式命令行界面：

```
> help
=== Plugin Host Commands ===
help, h          - Show this help message
list, ls         - List all loaded plugins
load <path>      - Load a plugin from file path
unload <name>    - Unload a plugin by name
execute <name> [input] - Execute a plugin with optional input
init <name> [key=value ...] - Initialize a plugin with config
quit, exit, q    - Exit the application
=============================

> list
Loaded Plugins (2):
Name                    Version         Description
----                    -------         -----------
example-plugin          1.0.0          Plugin loaded from ../plugins/example-plugin.so
calculator-plugin       1.0.0          Plugin loaded from ../plugins/calculator-plugin.so

> execute example-plugin hello
Executing plugin 'example-plugin' with input: 'hello'
Plugin output: Hello from example-plugin plugin (v1.0.0)!

> execute calculator-plugin 10 + 5
Executing plugin 'calculator-plugin' with input: '10 + 5'
Plugin output: Result: 15
```

## 插件开发

### 插件接口

所有插件必须实现 `shared.PluginInterface` 接口：

```go
type PluginInterface interface {
    GetName() string
    GetVersion() string
    Initialize(config map[string]string) error
    Execute(ctx context.Context, input string) (string, error)
    Cleanup() error
}
```

### 创建新插件

1. 在 `plugins/` 目录下创建新的插件目录
2. 实现 `PluginInterface` 接口
3. 在 `main()` 函数中启动插件服务

示例插件结构：

```go
package main

import (
    "context"
    "plugin.PVDIM/shared"
    "github.com/hashicorp/go-plugin"
)

type MyPlugin struct {
    // 插件字段
}

func (p *MyPlugin) GetName() string {
    return "my-plugin"
}

func (p *MyPlugin) GetVersion() string {
    return "1.0.0"
}

func (p *MyPlugin) Initialize(config map[string]string) error {
    // 初始化逻辑
    return nil
}

func (p *MyPlugin) Execute(ctx context.Context, input string) (string, error) {
    // 执行逻辑
    return "result", nil
}

func (p *MyPlugin) Cleanup() error {
    // 清理逻辑
    return nil
}

func main() {
    pluginImpl := &MyPlugin{}
    
    pluginMap := map[string]plugin.Plugin{
        shared.PluginName: &shared.PluginGRPC{Impl: pluginImpl},
    }

    plugin.Serve(&plugin.ServeConfig{
        HandshakeConfig: shared.Handshake,
        Plugins:         pluginMap,
        GRPCServer:      plugin.DefaultGRPCServer,
    })
}
```

### 构建插件

```bash
# 构建特定插件
make my-plugin

# 或使用 go build
go build -buildmode=plugin -o build/plugins/my-plugin.so ./plugins/my-plugin
```

## 内置插件

### Example Plugin
演示基本插件功能的示例插件。

**支持的命令:**
- `hello` - 返回问候消息
- `time` - 返回当前时间
- `config` - 显示插件配置
- `reverse:text` - 反转文本
- `upper:text` - 转换为大写
- `lower:text` - 转换为小写

### Calculator Plugin
简单的数学计算器插件。

**支持的操作:**
- 加法: `10 + 5`
- 减法: `10 - 5`
- 乘法: `10 * 5`
- 除法: `10 / 5`
- 幂运算: `2 ^ 3`

## API 参考

### 插件管理器方法

- `LoadPlugin(pluginPath string) error` - 加载插件
- `UnloadPlugin(name string) error` - 卸载插件
- `GetPlugin(name string) (PluginInterface, error)` - 获取插件实例
- `ListPlugins() []*PluginInfo` - 列出所有插件
- `ExecutePlugin(ctx, name, input string) (string, error)` - 执行插件
- `LoadPluginsFromDirectory(dir string) error` - 从目录加载插件
- `Shutdown()` - 关闭管理器

### 配置选项

插件可以通过 `Initialize()` 方法接收配置参数：

```bash
> init example-plugin greeting="你好" language="zh"
```

## 构建选项

```bash
make help           # 显示所有可用命令
make build          # 构建所有组件
make host           # 仅构建宿主程序
make plugins        # 构建所有插件
make example        # 构建示例插件
make calculator     # 构建计算器插件
make clean          # 清理构建文件
make test           # 运行测试
make package        # 创建分发包
```

## 故障排除

### 常见问题

1. **protoc 未找到**
   ```
   Warning: protoc not found. Please install Protocol Buffers compiler.
   ```
   解决方案：安装 Protocol Buffers 编译器

2. **插件加载失败**
   - 检查插件文件权限（需要可执行权限）
   - 确保插件是为正确的平台编译的
   - 检查插件是否实现了正确的接口

3. **gRPC 连接错误**
   - 确保防火墙没有阻止本地连接
   - 检查插件进程是否正常启动

### 调试模式

启用详细日志：

```bash
export PLUGIN_DEBUG=1
./plugin-host
```

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
