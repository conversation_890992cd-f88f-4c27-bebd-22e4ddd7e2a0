# Makefile for Plugin System

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build directories
BUILD_DIR=build
PLUGIN_DIR=$(BUILD_DIR)/plugins
HOST_DIR=$(BUILD_DIR)/host

# Binary names
HOST_BINARY=plugin-host
EXAMPLE_PLUGIN=example-plugin
CALCULATOR_PLUGIN=calculator-plugin

# Platform detection
UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Linux)
    PLUGIN_EXT=
    BUILD_FLAGS=
endif
ifeq ($(UNAME_S),Darwin)
    PLUGIN_EXT=
    BUILD_FLAGS=
endif
ifeq ($(OS),Windows_NT)
    PLUGIN_EXT=.exe
    BUILD_FLAGS=
    HOST_BINARY=plugin-host.exe
endif

.PHONY: all build clean test deps proto host plugins example calculator install run help

# Default target
all: deps proto build

# Install dependencies
deps:
	@echo "Installing dependencies..."
	$(GOMOD) tidy
	$(GOGET) github.com/hashicorp/go-plugin@latest
	$(GOGET) google.golang.org/grpc@latest
	$(GOGET) google.golang.org/protobuf@latest

# Generate protobuf files
proto:
	@echo "Generating protobuf files..."
	@if command -v protoc >/dev/null 2>&1; then \
		cd shared && protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		plugin.proto; \
	else \
		echo "Warning: protoc not found. Please install Protocol Buffers compiler."; \
		echo "On Ubuntu/Debian: sudo apt-get install protobuf-compiler"; \
		echo "On macOS: brew install protobuf"; \
		echo "On Windows: Download from https://github.com/protocolbuffers/protobuf/releases"; \
	fi

# Build all components
build: host plugins

# Create build directories
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)
	@mkdir -p $(PLUGIN_DIR)
	@mkdir -p $(HOST_DIR)

# Build host application
host: $(BUILD_DIR)
	@echo "Building host application..."
	$(GOBUILD) -o $(HOST_DIR)/$(HOST_BINARY) ./host

# Build all plugins
plugins: example calculator

# Build example plugin
example: $(BUILD_DIR)
	@echo "Building example plugin..."
	$(GOBUILD) $(BUILD_FLAGS) -o $(PLUGIN_DIR)/$(EXAMPLE_PLUGIN)$(PLUGIN_EXT) ./plugins/example

# Build calculator plugin
calculator: $(BUILD_DIR)
	@echo "Building calculator plugin..."
	$(GOBUILD) $(BUILD_FLAGS) -o $(PLUGIN_DIR)/$(CALCULATOR_PLUGIN)$(PLUGIN_EXT) ./plugins/calculator

# Install protoc plugins for Go
install-proto-deps:
	@echo "Installing protoc Go plugins..."
	$(GOGET) google.golang.org/protobuf/cmd/protoc-gen-go@latest
	$(GOGET) google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# Run the host application
run: build
	@echo "Starting plugin host..."
	cd $(HOST_DIR) && ./$(HOST_BINARY) ../plugins

# Run tests
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f shared/*.pb.go

# Development setup
dev-setup: install-proto-deps deps proto
	@echo "Development environment setup complete!"

# Package for distribution
package: build
	@echo "Creating distribution package..."
	@mkdir -p dist
	@tar -czf dist/plugin-system.tar.gz -C $(BUILD_DIR) .
	@echo "Package created: dist/plugin-system.tar.gz"

# Show help
help:
	@echo "Available targets:"
	@echo "  all              - Build everything (default)"
	@echo "  deps             - Install Go dependencies"
	@echo "  proto            - Generate protobuf files"
	@echo "  build            - Build host and all plugins"
	@echo "  host             - Build host application only"
	@echo "  plugins          - Build all plugins"
	@echo "  example          - Build example plugin only"
	@echo "  calculator       - Build calculator plugin only"
	@echo "  run              - Build and run the host application"
	@echo "  test             - Run tests"
	@echo "  clean            - Clean build artifacts"
	@echo "  dev-setup        - Setup development environment"
	@echo "  install-proto-deps - Install protobuf Go plugins"
	@echo "  package          - Create distribution package"
	@echo "  help             - Show this help message"

# Debug information
debug:
	@echo "Build configuration:"
	@echo "  OS: $(UNAME_S)"
	@echo "  Plugin extension: $(PLUGIN_EXT)"
	@echo "  Build flags: $(BUILD_FLAGS)"
	@echo "  Host binary: $(HOST_BINARY)"
