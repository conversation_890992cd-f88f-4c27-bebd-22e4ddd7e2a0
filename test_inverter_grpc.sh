#!/bin/bash

# 逆变器模拟器 gRPC 测试脚本

echo "🔌 逆变器模拟器 gRPC 测试"
echo "=========================="
echo ""

# 检查构建文件
echo "1. 检查构建文件..."
if [ ! -f "build/host/plugin-host" ]; then
    echo "❌ 宿主程序未找到，正在构建..."
    go build -o build/host/plugin-host ./host
fi

if [ ! -f "build/plugins/inverter-simulator" ]; then
    echo "❌ 逆变器模拟器插件未找到，正在构建..."
    go build -o build/plugins/inverter-simulator ./plugins/inverter-simulator
fi

echo "✅ 构建文件检查完成"

# 检查配置文件
echo "2. 检查配置文件..."
if [ ! -f "internal/config/inverter/config.ini" ]; then
    echo "❌ 配置文件不存在"
    exit 1
fi

if [ ! -f "internal/config/inverter/0.BiaoZhun.csv" ]; then
    echo "❌ 数据文件不存在"
    exit 1
fi

echo "✅ 配置文件检查完成"

# 设置权限
echo "3. 设置文件权限..."
chmod +x build/host/plugin-host
chmod +x build/plugins/inverter-simulator

echo "✅ 权限设置完成"

# 启动插件宿主程序
echo "4. 启动插件宿主程序..."
cd build/host

# 创建测试命令文件
cat > test_grpc_commands.txt << 'EOF'
list
init inverter-simulator config_dir=../../internal/config/inverter
execute inverter-simulator "status"
execute inverter-simulator "grpc-address"
execute inverter-simulator "help"
EOF

echo "启动宿主程序并获取 gRPC 地址..."

# 启动宿主程序并在后台运行
./plugin-host ../plugins < test_grpc_commands.txt > host_output.log 2>&1 &
HOST_PID=$!

# 等待启动
sleep 3

# 从日志中提取 gRPC 地址
echo "5. 获取 gRPC 服务地址..."
GRPC_ADDR=""
for i in {1..10}; do
    if [ -f "host_output.log" ]; then
        # 尝试从日志中提取地址
        GRPC_ADDR=$(grep -o "gRPC 服务器启动在端口: [^[:space:]]*" host_output.log | tail -1 | cut -d' ' -f4)
        if [ ! -z "$GRPC_ADDR" ]; then
            break
        fi
    fi
    sleep 1
done

if [ -z "$GRPC_ADDR" ]; then
    echo "❌ 无法获取 gRPC 服务地址"
    echo "宿主程序输出:"
    cat host_output.log
    kill $HOST_PID 2>/dev/null
    exit 1
fi

echo "✅ gRPC 服务地址: $GRPC_ADDR"

# 构建 gRPC 客户端
echo "6. 构建 gRPC 客户端..."
cd ../../
go build -o build/inverter-client ./cmd/inverter-client

echo "✅ gRPC 客户端构建完成"

# 测试 gRPC 接口
echo "7. 测试 gRPC 接口..."
echo ""

echo "📋 测试初始化设备..."
./build/inverter-client -addr "$GRPC_ADDR" -cmd init -position 1 -protocol biaozhun -comm "COM1,9600,1"
echo ""

echo "📋 测试获取数据..."
./build/inverter-client -addr "$GRPC_ADDR" -cmd get -position 1 -param 61697
echo ""

echo "📋 测试设置数据..."
./build/inverter-client -addr "$GRPC_ADDR" -cmd set -position 1 -param 61697 -value 200
echo ""

echo "📋 测试获取修改后的数据..."
./build/inverter-client -addr "$GRPC_ADDR" -cmd get -position 1 -param 61697
echo ""

echo "📋 测试修改通信参数..."
./build/inverter-client -addr "$GRPC_ADDR" -cmd modcomm -position 1 -comm "COM2,19200,2"
echo ""

echo "📋 测试修改协议..."
./build/inverter-client -addr "$GRPC_ADDR" -cmd modproto -position 1 -protocol huawei1
echo ""

echo "📋 测试清理设备..."
./build/inverter-client -addr "$GRPC_ADDR" -cmd cleanup -position 1
echo ""

# 清理
echo "8. 清理资源..."
kill $HOST_PID 2>/dev/null
wait $HOST_PID 2>/dev/null

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📋 功能说明："
echo "- 逆变器模拟器插件作为 gRPC 微服务运行"
echo "- 支持通过 gRPC 接口操作模拟设备"
echo "- 设备支持 Modbus RTU 协议通信"
echo "- 提供完整的设备生命周期管理"
echo ""
echo "🎯 gRPC 接口："
echo "- Initialize: 初始化逆变器设备"
echo "- Cleanup: 清理逆变器设备"
echo "- GetData: 获取设备数据"
echo "- SetData: 设置设备数据"
echo "- ModifyCommParam: 修改通信参数"
echo "- ModifyProtocol: 修改协议"
echo ""
echo "🔧 使用方法："
echo "1. 启动插件宿主程序"
echo "2. 初始化逆变器模拟器插件"
echo "3. 获取 gRPC 服务地址"
echo "4. 使用 gRPC 客户端连接并操作设备"
echo ""
echo "📚 更多信息请查看: internal/device/inverter/README.md"
