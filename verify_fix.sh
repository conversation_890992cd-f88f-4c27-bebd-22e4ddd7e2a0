#!/bin/bash

# 验证第89行修复的脚本

echo "🔧 验证第89行修复"
echo "=================="
echo ""

echo "1. 检查构建状态..."
if [ -f "build/inverter-client" ]; then
    echo "✅ gRPC 客户端构建成功"
else
    echo "❌ gRPC 客户端构建失败"
    exit 1
fi

if [ -f "build/plugins/inverter-simulator" ]; then
    echo "✅ 逆变器模拟器插件构建成功"
else
    echo "❌ 逆变器模拟器插件构建失败"
    exit 1
fi

echo ""
echo "2. 检查代码修复..."

# 检查客户端代码是否使用了正确的类型
if grep -q "PositionConfig" cmd/inverter-client/main.go; then
    echo "✅ 客户端代码已修复 - 使用正确的 PositionConfig 类型"
else
    echo "❌ 客户端代码未修复"
    exit 1
fi

# 检查是否包含所有必需的字段
if grep -q "CommonType:" cmd/inverter-client/main.go; then
    echo "✅ 客户端代码包含 CommonType 字段"
else
    echo "❌ 客户端代码缺少 CommonType 字段"
    exit 1
fi

if grep -q "CommonParam:" cmd/inverter-client/main.go; then
    echo "✅ 客户端代码包含 CommonParam 字段"
else
    echo "❌ 客户端代码缺少 CommonParam 字段"
    exit 1
fi

echo ""
echo "3. 验证 protobuf 定义..."

if grep -q "message PositionConfig" shared/device/inverter.proto; then
    echo "✅ protobuf 定义包含 PositionConfig 消息"
else
    echo "❌ protobuf 定义缺少 PositionConfig 消息"
    exit 1
fi

if grep -q "PositionConfig positions" shared/device/inverter.proto; then
    echo "✅ InverterInitializeRequest 使用正确的 PositionConfig 类型"
else
    echo "❌ InverterInitializeRequest 类型定义错误"
    exit 1
fi

echo ""
echo "4. 检查生成的 Go 代码..."

if [ -f "shared/device/inverter.pb.go" ]; then
    echo "✅ protobuf Go 代码已生成"
    
    if grep -q "type PositionConfig struct" shared/device/inverter.pb.go; then
        echo "✅ PositionConfig 结构体已生成"
    else
        echo "❌ PositionConfig 结构体未生成"
        exit 1
    fi
else
    echo "❌ protobuf Go 代码未生成"
    exit 1
fi

echo ""
echo "5. 显示修复后的关键代码..."
echo "客户端初始化请求 (第89行附近):"
echo "----------------------------------------"
sed -n '88,95p' cmd/inverter-client/main.go
echo "----------------------------------------"

echo ""
echo "protobuf 定义:"
echo "----------------------------------------"
grep -A 6 "message PositionConfig" shared/device/inverter.proto
echo "----------------------------------------"

echo ""
echo "=== 修复验证完成 ==="
echo ""
echo "📋 修复总结："
echo "- ✅ 第89行类型错误已修复"
echo "- ✅ 使用正确的 PositionConfig 类型"
echo "- ✅ 包含所有必需的字段"
echo "- ✅ 客户端和插件都已重新构建"
echo ""
echo "🎯 修复内容："
echo "- 将 device.Positions 改为 device.PositionConfig"
echo "- 添加 CommonType 字段 (默认值 0 表示串口通信)"
echo "- 正确映射 CommonParam 和 Protocol 字段"
echo ""
echo "✅ 现在可以正常使用 gRPC 客户端与逆变器模拟器通信了！"
