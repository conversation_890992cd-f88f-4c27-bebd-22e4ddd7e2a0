#!/bin/bash

# 测试插件系统的脚本

echo "=== 插件系统测试 ==="

# 检查构建文件是否存在
echo "1. 检查构建文件..."
if [ ! -f "build/host/plugin-host" ]; then
    echo "❌ 宿主程序未找到"
    exit 1
fi

if [ ! -f "build/plugins/example-plugin.so" ]; then
    echo "❌ 示例插件未找到"
    exit 1
fi

if [ ! -f "build/plugins/calculator-plugin.so" ]; then
    echo "❌ 计算器插件未找到"
    exit 1
fi

echo "✅ 所有构建文件存在"

# 检查文件权限
echo "2. 检查文件权限..."
chmod +x build/host/plugin-host
chmod +x build/plugins/*.so

echo "✅ 文件权限设置完成"

# 测试插件加载
echo "3. 测试插件系统..."
echo "启动插件宿主程序..."

cd build/host

# 创建测试输入文件
cat > test_commands.txt << 'EOF'
list
execute example-plugin hello
execute calculator-plugin 10 + 5
execute calculator-plugin 2 ^ 3
execute example-plugin time
quit
EOF

echo "执行测试命令..."
timeout 30s ./plugin-host ../plugins < test_commands.txt

echo ""
echo "=== 测试完成 ==="
echo ""
echo "如果看到插件列表和执行结果，说明系统工作正常！"
echo ""
echo "手动测试命令："
echo "cd build/host && ./plugin-host ../plugins"
