package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"time"

	"plugin.PVDIM/shared/device"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func main() {
	var (
		addr      = flag.String("addr", "localhost:0", "gRPC server address")
		cmd       = flag.String("cmd", "help", "command to execute")
		position  = flag.Int("position", 1, "device position")
		protocol  = flag.String("protocol", "biaozhun", "device protocol")
		commParam = flag.String("comm", "COM1,9600,1", "communication parameters")
		paramType = flag.Int("param", 61697, "parameter type")
		value     = flag.String("value", "100", "value to set")
	)
	flag.Parse()

	if *addr == "localhost:0" {
		fmt.Println("请指定 gRPC 服务器地址，例如: -addr localhost:12345")
		fmt.Println("使用方法:")
		fmt.Println("  go run cmd/inverter-client/main.go -addr <address> -cmd <command> [options]")
		fmt.Println("")
		fmt.Println("可用命令:")
		fmt.Println("  init     - 初始化设备")
		fmt.Println("  cleanup  - 清理设备")
		fmt.Println("  get      - 获取数据")
		fmt.Println("  set      - 设置数据")
		fmt.Println("  modcomm  - 修改通信参数")
		fmt.Println("  modproto - 修改协议")
		fmt.Println("")
		fmt.Println("示例:")
		fmt.Println("  go run cmd/inverter-client/main.go -addr localhost:12345 -cmd init -position 1 -protocol biaozhun -comm 'COM1,9600,1'")
		fmt.Println("  go run cmd/inverter-client/main.go -addr localhost:12345 -cmd get -position 1 -param 61697")
		fmt.Println("  go run cmd/inverter-client/main.go -addr localhost:12345 -cmd set -position 1 -param 61697 -value 100")
		return
	}

	// 连接到 gRPC 服务器
	conn, err := grpc.Dial(*addr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("连接失败: %v", err)
	}
	defer conn.Close()

	// 创建客户端
	client := device.NewInverterServiceClient(conn)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 执行命令
	switch *cmd {
	case "init":
		err = initializeDevice(ctx, client, *position, *protocol, *commParam)
	case "cleanup":
		err = cleanupDevice(ctx, client, *position)
	case "get":
		err = getData(ctx, client, *position, *paramType)
	case "set":
		err = setData(ctx, client, *position, *paramType, *value)
	case "modcomm":
		err = modifyCommParam(ctx, client, *position, *commParam)
	case "modproto":
		err = modifyProtocol(ctx, client, *position, *protocol)
	default:
		fmt.Printf("未知命令: %s\n", *cmd)
		return
	}

	if err != nil {
		log.Fatalf("执行命令失败: %v", err)
	}
}

// initializeDevice 初始化设备
func initializeDevice(ctx context.Context, client device.InverterServiceClient, position int, protocol, commParam string) error {
	fmt.Printf("初始化设备 [表位%d] 协议: %s, 通信参数: %s\n", position, protocol, commParam)

	req := &device.InverterInitializeRequest{
		Positions: &device.PositionConfig{
			Position:    int32(position),
			CommonType:  0, // 默认串口通信
			CommonParam: commParam,
			Protocol:    protocol,
		},
	}

	resp, err := client.Initialize(ctx, req)
	if err != nil {
		return fmt.Errorf("初始化失败: %v", err)
	}

	fmt.Printf("初始化成功，设备表位: %d\n", resp.Position)
	return nil
}

// cleanupDevice 清理设备
func cleanupDevice(ctx context.Context, client device.InverterServiceClient, position int) error {
	fmt.Printf("清理设备 [表位%d]\n", position)

	req := &device.InverterCleanupRequest{
		Position: int32(position),
	}

	_, err := client.Cleanup(ctx, req)
	if err != nil {
		return fmt.Errorf("清理失败: %v", err)
	}

	fmt.Printf("清理成功\n")
	return nil
}

// getData 获取数据
func getData(ctx context.Context, client device.InverterServiceClient, position, paramType int) error {
	fmt.Printf("获取数据 [表位%d] 参数类型: %d\n", position, paramType)

	req := &device.InverterGetDataRequest{
		Position:  int32(position),
		ParamType: int32(paramType),
	}

	resp, err := client.GetData(ctx, req)
	if err != nil {
		return fmt.Errorf("获取数据失败: %v", err)
	}

	fmt.Printf("获取数据成功:\n")
	fmt.Printf("  表位: %d\n", resp.Position)
	fmt.Printf("  参数类型: %d\n", resp.ParamType)
	fmt.Printf("  值: %s\n", resp.Value)
	return nil
}

// setData 设置数据
func setData(ctx context.Context, client device.InverterServiceClient, position, paramType int, value string) error {
	fmt.Printf("设置数据 [表位%d] 参数类型: %d, 值: %s\n", position, paramType, value)

	req := &device.InverterSetDataRequest{
		Position:  int32(position),
		ParamType: int32(paramType),
		Value:     value,
	}

	_, err := client.SetData(ctx, req)
	if err != nil {
		return fmt.Errorf("设置数据失败: %v", err)
	}

	fmt.Printf("设置数据成功\n")
	return nil
}

// modifyCommParam 修改通信参数
func modifyCommParam(ctx context.Context, client device.InverterServiceClient, position int, commParam string) error {
	fmt.Printf("修改通信参数 [表位%d] 参数: %s\n", position, commParam)

	req := &device.InverterModifyCommParamRequest{
		Position:    int32(position),
		CommonType:  1, // 默认类型
		CommonParam: commParam,
	}

	_, err := client.ModifyCommParam(ctx, req)
	if err != nil {
		return fmt.Errorf("修改通信参数失败: %v", err)
	}

	fmt.Printf("修改通信参数成功\n")
	return nil
}

// modifyProtocol 修改协议
func modifyProtocol(ctx context.Context, client device.InverterServiceClient, position int, protocol string) error {
	fmt.Printf("修改协议 [表位%d] 协议: %s\n", position, protocol)

	req := &device.InverterModifyProtocolRequest{
		Position: int32(position),
		Protocol: protocol,
	}

	_, err := client.ModifyProtocol(ctx, req)
	if err != nil {
		return fmt.Errorf("修改协议失败: %v", err)
	}

	fmt.Printf("修改协议成功\n")
	return nil
}
