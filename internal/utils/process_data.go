package utils

// 将字符串按字节进行倒置
func ReverseString(s string) string {
	runes := []byte(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

// 将字符串按字节进行倒置返回 byte 数组
func ReverseStringToBytes(s string) []byte {
	runes := []byte(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return runes
}
