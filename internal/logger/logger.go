package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"plugin.PVDIM/internal/config"
)

var (
	logFile     *os.File
	logLevel    Level
	consoleMode bool
)

// Level 表示日志级别
type Level int

const (
	DEBUG Level = iota
	INFO
	WARN
	ERROR
	FATAL
)

// 将字符串转换为日志级别
func parseLevel(level string) Level {
	switch strings.ToUpper(level) {
	case "DEBUG":
		return DEBUG
	case "INFO":
		return INFO
	case "WARN":
		return WARN
	case "ERROR":
		return ERROR
	case "FATAL":
		return FATAL
	default:
		return INFO // 默认为 INFO 级别
	}
}

// 获取日志级别的字符串表示
func (l Level) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	case FATAL:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// InitWithConfig 使用配置初始化日志系统
func InitWithConfig(cfg *config.LoggingConfig) error {
	// 检查配置是否有效
	if cfg == nil {
		return fmt.Errorf("日志配置为空")
	}

	// 检查文件路径是否有效
	if cfg.FilePath == "" {
		return fmt.Errorf("日志文件路径为空")
	}

	// 设置日志级别
	logLevel = parseLevel(cfg.Level)
	consoleMode = cfg.Console

	// 创建日志目录
	dir := filepath.Dir(cfg.FilePath)
	if dir != "" && dir != "." {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建日志目录失败: %w", err)
		}
	}

	// 打开日志文件
	file, err := os.OpenFile(cfg.FilePath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %w", err)
	}

	// 关闭之前的日志文件
	if logFile != nil {
		logFile.Close()
	}

	logFile = file

	// 设置输出
	if consoleMode {
		// 同时输出到控制台和文件
		log.SetOutput(io.MultiWriter(os.Stdout, file))
	} else {
		// 只输出到文件
		log.SetOutput(file)
	}

	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds)
	log.Printf("[INFO] 日志系统初始化完成，级别: %s, 文件: %s, 控制台输出: %v", logLevel, cfg.FilePath, consoleMode)

	return nil
}

// Init 初始化日志系统（兼容旧接口）
func Init(filePath string) error {
	// 检查文件路径是否有效
	if filePath == "" {
		filePath = "logs/app.log" // 使用默认路径
	}

	// 创建日志目录
	dir := filepath.Dir(filePath)
	if dir != "" && dir != "." {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建日志目录失败: %w", err)
		}
	}

	// 打开日志文件
	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %w", err)
	}

	// 关闭之前的日志文件
	if logFile != nil {
		logFile.Close()
	}

	logFile = file

	// 设置默认值
	logLevel = INFO
	consoleMode = true

	// 同时输出到控制台和文件
	log.SetOutput(io.MultiWriter(os.Stdout, file))
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds)

	log.Printf("[INFO] 日志系统初始化完成，使用默认配置，文件: %s", filePath)

	return nil
}

// Close 关闭日志系统
func Close() error {
	if logFile != nil {
		return logFile.Close()
	}
	return nil
}

// shouldLog 判断是否应该记录指定级别的日志
func shouldLog(level Level) bool {
	return level >= logLevel
}

// Debug 记录调试级别日志
func Debug(format string, args ...interface{}) {
	if shouldLog(DEBUG) {
		log.Printf("[DEBUG] "+format, args...)
	}
}

// Info 记录信息级别日志
func Info(format string, args ...interface{}) {
	if shouldLog(INFO) {
		log.Printf("[INFO] "+format, args...)
	}
}

// Warn 记录警告级别日志
func Warn(format string, args ...interface{}) {
	if shouldLog(WARN) {
		log.Printf("[WARN] "+format, args...)
	}
}

// Error 记录错误级别日志
func Error(format string, args ...interface{}) {
	if shouldLog(ERROR) {
		log.Printf("[ERROR] "+format, args...)
	}
}

// Fatal 记录致命错误级别日志，并退出程序
func Fatal(format string, args ...interface{}) {
	if shouldLog(FATAL) {
		log.Fatalf("[FATAL] "+format, args...)
	}
}

// API 记录API访问日志
func API(method, path string, statusCode int, duration time.Duration) {
	if shouldLog(INFO) {
		log.Printf("[API] %s %s - %d - %v", method, path, statusCode, duration)
	}
}

// WebSocket 记录WebSocket消息日志
func WebSocket(messageType string, dataLength int, details string) {
	if shouldLog(DEBUG) {
		log.Printf("[WS] %s - 长度: %d字节 - %s", messageType, dataLength, details)
	}
}

// Detection 记录检测相关日志
func Detection(positionID int, itemID int, status string, details string) {
	if shouldLog(INFO) {
		log.Printf("[检测] 表位%d - 项目%d - %s - %s", positionID, itemID, status, details)
	}
}

// Result 记录检测结果日志
func Result(positionID int, itemID int, success bool, details string) {
	level := INFO
	if !success {
		level = WARN
	}

	if shouldLog(level) {
		status := "成功"
		if !success {
			status = "失败"
		}
		log.Printf("[结果] 表位%d - 项目%d - %s - %s", positionID, itemID, status, details)
	}
}

// 初始化日志系统
func init() {
	// 默认输出到控制台
	log.SetOutput(os.Stdout)
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds)
	logLevel = INFO
	consoleMode = true

	// 尝试创建日志文件
	logDir := "logs"
	if err := os.MkdirAll(logDir, 0755); err == nil {
		logPath := filepath.Join(logDir, fmt.Sprintf("app_%s.log", time.Now().Format("20060102")))
		if file, err := os.OpenFile(logPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644); err == nil {
			logFile = file
			// 同时输出到控制台和文件
			log.SetOutput(io.MultiWriter(os.Stdout, file))
		}
	}
}
