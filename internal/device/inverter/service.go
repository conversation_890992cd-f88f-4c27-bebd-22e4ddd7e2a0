package inverter

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"plugin.PVDIM/internal/logger"
	"plugin.PVDIM/shared/device"
)

// InverterService 逆变器服务实现
type InverterService struct {
	device.UnimplementedInverterServiceServer
	deviceManager *DeviceManager
	name          string
	version       string
	initialized   bool
}

// NewInverterService 创建逆变器服务
func NewInverterService(configDir string) (*InverterService, error) {
	deviceManager, err := NewDeviceManager(configDir)
	if err != nil {
		return nil, fmt.Errorf("创建设备管理器失败: %v", err)
	}

	return &InverterService{
		deviceManager: deviceManager,
		name:          "inverter-simulator",
		version:       "1.0.0",
	}, nil
}

// Initialize 初始化服务
func (s *InverterService) Initialize(ctx context.Context, req *device.InverterInitializeRequest) (*device.InverterInitializeResponse, error) {
	if s.initialized {
		return &device.InverterInitializeResponse{Position: req.Positions.Position}, nil
	}

	// 解析配置并创建模拟器
	position := req.Positions.Position
	protocol := req.Positions.Protocol
	commParam := req.Positions.CommonParam

	// 解析通信参数 (格式: "port:COM1,baudrate:9600,slaveid:1")
	params := parseCommParams(commParam)
	port, exists := params["port"]
	if !exists {
		return nil, fmt.Errorf("缺少端口参数")
	}

	baudRate := 9600
	if br, exists := params["baudrate"]; exists {
		if rate, err := strconv.Atoi(br); err == nil {
			baudRate = rate
		}
	}

	slaveID := byte(1)
	if sid, exists := params["slaveid"]; exists {
		if id, err := strconv.Atoi(sid); err == nil && id >= 1 && id <= 247 {
			slaveID = byte(id)
		}
	}

	// 创建模拟器
	if err := s.deviceManager.CreateSimulator(position, protocol, slaveID); err != nil {
		return nil, fmt.Errorf("创建模拟器失败: %v", err)
	}

	// 启动模拟器
	if err := s.deviceManager.StartSimulator(position, port, baudRate); err != nil {
		return nil, fmt.Errorf("启动模拟器失败: %v", err)
	}

	s.initialized = true
	logger.Info("逆变器服务初始化成功 [表位%d] 协议: %s, 端口: %s", position, protocol, port)

	return &device.InverterInitializeResponse{Position: position}, nil
}

// Cleanup 清理资源
func (s *InverterService) Cleanup(ctx context.Context, req *device.InverterCleanupRequest) (*device.InverterCleanupResponse, error) {
	if s.deviceManager != nil {
		if err := s.deviceManager.StopAll(); err != nil {
			logger.Error("停止所有模拟器失败: %v", err)
		}
	}

	s.initialized = false
	logger.Info("逆变器服务已清理")

	return &device.InverterCleanupResponse{}, nil
}

// GetData 获取数据
func (s *InverterService) GetData(ctx context.Context, req *device.GetDataRequest) (*device.GetDataResponse, error) {
	if !s.initialized {
		return nil, fmt.Errorf("服务未初始化")
	}

	// 根据参数类型获取数据
	value, err := s.deviceManager.GetRegisterValue(req.Position, 3, uint16(req.ParamType))
	if err != nil {
		return nil, fmt.Errorf("获取数据失败: %v", err)
	}

	return &device.GetDataResponse{
		Position:  req.Position,
		ParamType: req.ParamType,
		Value:     fmt.Sprintf("%d", value),
	}, nil
}

// SetData 设置数据
func (s *InverterService) SetData(ctx context.Context, req *device.SetDataRequest) (*device.SetDataResponse, error) {
	if !s.initialized {
		return nil, fmt.Errorf("服务未初始化")
	}

	// 解析值
	value, err := strconv.ParseUint(req.Value, 10, 16)
	if err != nil {
		return nil, fmt.Errorf("无效的值: %v", err)
	}

	// 设置寄存器值
	if err := s.deviceManager.SetRegisterValue(req.Position, 3, uint16(req.ParamType), uint16(value)); err != nil {
		return nil, fmt.Errorf("设置数据失败: %v", err)
	}

	return &device.SetDataResponse{}, nil
}

// ModifyCommParam 修改通信参数
func (s *InverterService) ModifyCommParam(ctx context.Context, req *device.ModifyCommParamRequest) (*device.ModifyCommParamResponse, error) {
	// 这里可以实现修改通信参数的逻辑
	logger.Info("修改通信参数 [表位%d] 类型: %d, 参数: %s", req.Position, req.CommonType, req.CommonParam)
	return &device.ModifyCommParamResponse{}, nil
}

// ModifyProtocol 修改协议
func (s *InverterService) ModifyProtocol(ctx context.Context, req *device.ModifyProtocolRequest) (*device.ModifyProtocolResponse, error) {
	// 这里可以实现修改协议的逻辑
	logger.Info("修改协议 [表位%d] 协议: %s", req.Position, req.Protocol)
	return &device.ModifyProtocolResponse{}, nil
}

// parseCommParams 解析通信参数
func parseCommParams(commParam string) map[string]string {
	params := make(map[string]string)

	// 支持多种格式: "port:COM1,baudrate:9600,slaveid:1" 或 "COM1,9600,1"
	if strings.Contains(commParam, ":") {
		// 键值对格式
		pairs := strings.Split(commParam, ",")
		for _, pair := range pairs {
			kv := strings.SplitN(strings.TrimSpace(pair), ":", 2)
			if len(kv) == 2 {
				params[strings.TrimSpace(kv[0])] = strings.TrimSpace(kv[1])
			}
		}
	} else {
		// 简单格式
		parts := strings.Split(commParam, ",")
		if len(parts) >= 1 {
			params["port"] = strings.TrimSpace(parts[0])
		}
		if len(parts) >= 2 {
			params["baudrate"] = strings.TrimSpace(parts[1])
		}
		if len(parts) >= 3 {
			params["slaveid"] = strings.TrimSpace(parts[2])
		}
	}

	return params
}
