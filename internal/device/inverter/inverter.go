package inverter

import (
	"encoding/hex"
	"fmt"
	"os"
	"path/filepath"
	"syscall"
	"unsafe"

	"github.com/sirupsen/logrus"
)

var (
	kernel32              = syscall.NewLazyDLL("kernel32.dll")
	procSetDllDirectoryW  = kernel32.NewProc("SetDllDirectoryW")
	procAddDllDirectory   = kernel32.NewProc("AddDllDirectory")
	procSetDefaultDllDirs = kernel32.NewProc("SetDefaultDllDirectories")
)

// SetDllDirectory 设置 DLL 搜索目录
func SetDllDirectory(dir string) error {
	ptr, err := syscall.UTF16PtrFromString(dir)
	if err != nil {
		return err
	}
	ret, _, err := procSetDllDirectoryW.Call(uintptr(unsafe.Pointer(ptr)))
	if ret == 0 {
		return err
	}
	return nil
}

type InverterDLLOperation struct {
	dirDLL      string
	libInverter *syscall.DLL
}

func NewInverter() *InverterDLLOperation {
	return &InverterDLLOperation{}
}

func (i *InverterDLLOperation) Connect(dirDLL string) error {
	if dirDLL == "" {
		execPath, err := os.Executable()
		if err != nil {
			return fmt.Errorf("获取执行路径失败：%v", err)
		}
		dirDLL = filepath.Join(filepath.Dir(execPath), "dll")
	}

	// 设置 DLL 搜索目录
	err := SetDllDirectory(dirDLL)
	if err != nil {
		return fmt.Errorf("设置 DLL 搜索目录失败：%v", err)
	}

	i.dirDLL = dirDLL
	logrus.Debugf("DLL 加载路径：%s", i.dirDLL)
	dllPath := filepath.Join(i.dirDLL, "analogInverter4.dll")
	dll, err := syscall.LoadDLL(dllPath)
	if err != nil {
		return fmt.Errorf("加载 DLL 失败：%v", err)
	}
	i.libInverter = dll
	return nil
}

func (i *InverterDLLOperation) Create(number int, protocol string) error {
	pathConfig := filepath.Join(i.dirDLL, "..", "config", "config.ini")
	mbCreate, err := i.libInverter.FindProc("mbCreate")
	if err != nil {
		return fmt.Errorf("查找 mbCreate 函数失败：%v", err)
	}
	ansiStr1 := append([]byte(protocol), 0)
	ansiStr2 := append([]byte(pathConfig), 0)
	ret, _, _ := mbCreate.Call(
		uintptr(number),
		uintptr(unsafe.Pointer(&ansiStr1[0])),
		uintptr(unsafe.Pointer(&ansiStr2[0])),
	)
	if ret != 0 {
		return fmt.Errorf("创建 %d 检测位逆变器失败：%v", number, ret)
	}
	return nil
}

func (i *InverterDLLOperation) Release(number int) error {
	mbRelease, err := i.libInverter.FindProc("mbRelease")
	if err != nil {
		return fmt.Errorf("查找 mbRelease 函数失败：%v", err)
	}
	mbRelease.Call(uintptr(number))
	return nil
}

func (i *InverterDLLOperation) Parse(number int, data string) (string, error) {
	// 处理输入数据
	inData, err := hex.DecodeString(data)
	if err != nil {
		return "", fmt.Errorf("处理 %d 检测位 modbus 传输数据失败：%v", number, err)
	}
	inDataLen := len(inData)
	// 输出数据指针创建
	outputData := make([]byte, 1000)
	outLen := 0
	outLenPtr := &outLen
	mbDataParse, err := i.libInverter.FindProc("mbDataPrase")
	if err != nil {
		return "", fmt.Errorf("查找 mbDataPrase 函数失败：%v", err)
	}
	ret, _, _ := mbDataParse.Call(
		uintptr(number),
		uintptr(unsafe.Pointer(&inData[0])),
		uintptr(inDataLen),
		uintptr(unsafe.Pointer(&outputData[0])),
		uintptr(unsafe.Pointer(outLenPtr)))
	if ret != 0 {
		return "", fmt.Errorf("处理 %d 检测位 modbus 传输数据失败：%v", number, ret)
	}
	resData := hex.EncodeToString(outputData[:*outLenPtr])
	return resData, nil
}
