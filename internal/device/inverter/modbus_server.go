package inverter

import (
	"encoding/binary"
	"fmt"
	"sync"
	"time"

	"plugin.PVDIM/internal/common"
	"plugin.PVDIM/internal/logger"
)

// Modbus功能码
const (
	FuncReadCoils              byte = 0x01
	FuncReadDiscreteInputs     byte = 0x02
	FuncReadHoldingRegisters   byte = 0x03
	FuncReadInputRegisters     byte = 0x04
	FuncWriteSingleCoil        byte = 0x05
	FuncWriteSingleRegister    byte = 0x06
	FuncWriteMultipleCoils     byte = 0x0F
	FuncWriteMultipleRegisters byte = 0x10
)

// Modbus异常码
const (
	ExceptionIllegalFunction     byte = 0x01
	ExceptionIllegalDataAddress  byte = 0x02
	ExceptionIllegalDataValue    byte = 0x03
	ExceptionServerDeviceFailure byte = 0x04
)

// ModbusRequest Modbus请求
type ModbusRequest struct {
	SlaveID      byte
	FunctionCode byte
	Data         []byte
}

// ModbusResponse Modbus响应
type ModbusResponse struct {
	SlaveID      byte
	FunctionCode byte
	Data         []byte
	Exception    byte
}

// RequestHandler 请求处理器函数类型
type RequestHandler func(*ModbusRequest) *ModbusResponse

// ModbusServer Modbus服务器
type ModbusServer struct {
	port           string
	baudRate       int
	slaveID        byte
	serialPort     *common.SerialPort
	requestHandler RequestHandler
	running        bool
	mutex          sync.RWMutex
	stopChan       chan struct{}
}

// NewModbusServer 创建Modbus服务器
func NewModbusServer(port string, baudRate int, slaveID byte) (*ModbusServer, error) {
	config := common.SerialConfig{
		Port:     port,
		BaudRate: baudRate,
		DataBits: 8,
		Parity:   "N",
		StopBits: 1,
		Timeout:  3000, // 3秒超时，单位毫秒
	}

	serialPort := common.NewSerialPort(config)

	return &ModbusServer{
		port:       port,
		baudRate:   baudRate,
		slaveID:    slaveID,
		serialPort: serialPort,
		stopChan:   make(chan struct{}),
	}, nil
}

// SetRequestHandler 设置请求处理器
func (s *ModbusServer) SetRequestHandler(handler RequestHandler) {
	s.requestHandler = handler
}

// Start 启动服务器
func (s *ModbusServer) Start() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		return fmt.Errorf("服务器已在运行")
	}

	// 打开串口
	if err := s.serialPort.Open(); err != nil {
		return fmt.Errorf("打开串口失败: %v", err)
	}

	s.running = true
	s.stopChan = make(chan struct{})

	// 启动监听协程
	go s.listen()

	logger.Info("Modbus服务器已启动，端口: %s, 波特率: %d, 从站地址: %d",
		s.port, s.baudRate, s.slaveID)

	return nil
}

// Stop 停止服务器
func (s *ModbusServer) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return nil
	}

	close(s.stopChan)
	s.running = false

	if s.serialPort.IsOpen() {
		if err := s.serialPort.Close(); err != nil {
			logger.Error("关闭串口失败: %v", err)
		}
	}

	logger.Info("Modbus服务器已停止")
	return nil
}

// IsRunning 检查是否正在运行
func (s *ModbusServer) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.running
}

// listen 监听串口数据
func (s *ModbusServer) listen() {
	buffer := make([]byte, 256)

	for {
		select {
		case <-s.stopChan:
			return
		default:
			// 读取数据
			n, err := s.serialPort.ReadWithTimeout(buffer, time.Millisecond*100)
			if err != nil {
				if s.IsRunning() {
					logger.Debug("读取串口数据失败: %v", err)
				}
				continue
			}

			if n > 0 {
				// 处理接收到的数据
				s.handleReceivedData(buffer[:n])
			}
		}
	}
}

// handleReceivedData 处理接收到的数据
func (s *ModbusServer) handleReceivedData(data []byte) {
	// 解析Modbus请求
	request, err := s.parseModbusRequest(data)
	if err != nil {
		logger.Debug("解析Modbus请求失败: %v", err)
		return
	}

	// 检查从站地址
	if request.SlaveID != s.slaveID {
		// 不是发给本从站的请求，忽略
		return
	}

	// 处理请求
	var response *ModbusResponse
	if s.requestHandler != nil {
		response = s.requestHandler(request)
	} else {
		// 默认响应：不支持的功能
		response = &ModbusResponse{
			SlaveID:   request.SlaveID,
			Exception: ExceptionIllegalFunction,
		}
	}

	// 发送响应
	if response != nil {
		responseData := s.buildModbusResponse(response)
		if _, err := s.serialPort.Write(responseData); err != nil {
			logger.Error("发送Modbus响应失败: %v", err)
		}
	}
}

// parseModbusRequest 解析Modbus请求
func (s *ModbusServer) parseModbusRequest(data []byte) (*ModbusRequest, error) {
	if len(data) < 4 {
		return nil, fmt.Errorf("数据长度不足")
	}

	// 验证CRC
	if !s.verifyCRC(data) {
		return nil, fmt.Errorf("CRC校验失败")
	}

	request := &ModbusRequest{
		SlaveID:      data[0],
		FunctionCode: data[1],
		Data:         data[2 : len(data)-2], // 去掉CRC
	}

	return request, nil
}

// buildModbusResponse 构建Modbus响应
func (s *ModbusServer) buildModbusResponse(response *ModbusResponse) []byte {
	var data []byte

	if response.Exception != 0 {
		// 异常响应
		data = make([]byte, 5)
		data[0] = response.SlaveID
		data[1] = response.FunctionCode | 0x80 // 设置异常标志
		data[2] = response.Exception
	} else {
		// 正常响应
		data = make([]byte, 2+len(response.Data)+2)
		data[0] = response.SlaveID
		data[1] = response.FunctionCode
		copy(data[2:], response.Data)
	}

	// 计算并添加CRC
	crc := s.calcCRC16(data[:len(data)-2])
	data[len(data)-2] = byte(crc & 0xFF)
	data[len(data)-1] = byte(crc >> 8)

	return data
}

// verifyCRC 验证CRC
func (s *ModbusServer) verifyCRC(data []byte) bool {
	if len(data) < 3 {
		return false
	}

	receivedCRC := binary.LittleEndian.Uint16(data[len(data)-2:])
	calculatedCRC := s.calcCRC16(data[:len(data)-2])

	return receivedCRC == calculatedCRC
}

// calcCRC16 计算Modbus CRC16校验和
func (s *ModbusServer) calcCRC16(data []byte) uint16 {
	crc := uint16(0xFFFF)
	for _, b := range data {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&0x0001 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}
