package inverter

type InverterParamType int

const (
	// 序列号
	SERIAL_NUMBER InverterParamType = iota
	// 额定有功
	RATED_ACTIVE_POWER
	// 额定无功
	RATED_REACTIVE_POWER
	// 输出类型
	OUTPUT_TYPE
	// L1 电压
	VOLTAGE_L1
	// L2 电压
	VOLTAGE_L2
	// L3 电压
	VOLTAGE_L3
	// A 相电流
	CURRENT_A
	// B 相电流
	CURRENT_B
	// C 相电流
	CURRENT_C
	// 功率因数
	POWER_FACTOR
	// 逆变器开关
	INVERTER_SWITCH
	// 有功功率控制
	ACTIVE_POWER_CONTROL

	// 有功功率百分比
	ACTIVE_POWER_PERCENTAGE
	// 无功功率控制
	REACTIVE_POWER_CONTROL
	// 无功功率百分比
	REACTIVE_POWER_PERCENTAGE

	// 运行状态
	OPERATIONAL_STATUS
	// 有功功率
	ACTIVE_POWER
	// 无功功率
	REACTIVE_POWER
)
