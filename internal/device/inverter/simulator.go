package inverter

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"plugin.PVDIM/internal/logger"
)

// InverterSimulator 逆变器模拟器
type InverterSimulator struct {
	position       int32             // 表位号
	config         *InverterConfig   // 配置信息
	slaveID        byte              // Modbus从站地址
	server         *ModbusServer     // Modbus服务器
	running        bool              // 运行状态
	mutex          sync.RWMutex      // 读写锁
	registers      map[uint16]uint16 // 寄存器数据 (地址 -> 值)
	inputRegs      map[uint16]uint16 // 输入寄存器
	holdingRegs    map[uint16]uint16 // 保持寄存器
	coils          map[uint16]bool   // 线圈
	discreteInputs map[uint16]bool   // 离散输入
}

// NewInverterSimulator 创建逆变器模拟器
func NewInverterSimulator(position int32, config *InverterConfig, slaveID byte) *InverterSimulator {
	sim := &InverterSimulator{
		position:       position,
		config:         config,
		slaveID:        slaveID,
		registers:      make(map[uint16]uint16),
		inputRegs:      make(map[uint16]uint16),
		holdingRegs:    make(map[uint16]uint16),
		coils:          make(map[uint16]bool),
		discreteInputs: make(map[uint16]bool),
	}

	// 初始化寄存器数据
	sim.initializeRegisters()

	return sim
}

// initializeRegisters 初始化寄存器数据
func (sim *InverterSimulator) initializeRegisters() {
	sim.mutex.Lock()
	defer sim.mutex.Unlock()

	// 从配置中加载初始数据
	for address, regInfo := range sim.config.RegisterData {
		switch regInfo.FunctionCode {
		case 2: // 输入寄存器
			if regInfo.Length == 1 {
				sim.inputRegs[address] = uint16(regInfo.Value)
			} else if regInfo.Length == 2 {
				// 32位数据，分成两个16位寄存器
				if sim.config.LittleEndian {
					sim.inputRegs[address] = uint16(regInfo.Value & 0xFFFF)
					sim.inputRegs[address+1] = uint16(regInfo.Value >> 16)
				} else {
					sim.inputRegs[address] = uint16(regInfo.Value >> 16)
					sim.inputRegs[address+1] = uint16(regInfo.Value & 0xFFFF)
				}
			}
		case 3: // 保持寄存器
			if regInfo.Length == 1 {
				sim.holdingRegs[address] = uint16(regInfo.Value)
			} else if regInfo.Length == 2 {
				// 32位数据，分成两个16位寄存器
				if sim.config.LittleEndian {
					sim.holdingRegs[address] = uint16(regInfo.Value & 0xFFFF)
					sim.holdingRegs[address+1] = uint16(regInfo.Value >> 16)
				} else {
					sim.holdingRegs[address] = uint16(regInfo.Value >> 16)
					sim.holdingRegs[address+1] = uint16(regInfo.Value & 0xFFFF)
				}
			}
		}
	}

	logger.Info("逆变器模拟器 [表位%d] 初始化完成，加载了 %d 个寄存器",
		sim.position, len(sim.config.RegisterData))
}

// Start 启动模拟器
func (sim *InverterSimulator) Start(port string, baudRate int) error {
	sim.mutex.Lock()
	defer sim.mutex.Unlock()

	if sim.running {
		return fmt.Errorf("模拟器已在运行")
	}

	// 创建Modbus服务器
	server, err := NewModbusServer(port, baudRate, sim.slaveID)
	if err != nil {
		return fmt.Errorf("创建Modbus服务器失败: %v", err)
	}

	// 设置请求处理器
	server.SetRequestHandler(sim.handleModbusRequest)

	// 启动服务器
	if err := server.Start(); err != nil {
		return fmt.Errorf("启动Modbus服务器失败: %v", err)
	}

	sim.server = server
	sim.running = true

	logger.Info("逆变器模拟器 [表位%d] 已启动，监听端口: %s, 波特率: %d, 从站地址: %d",
		sim.position, port, baudRate, sim.slaveID)

	// 启动数据更新协程
	go sim.updateData()

	return nil
}

// Stop 停止模拟器
func (sim *InverterSimulator) Stop() error {
	sim.mutex.Lock()
	defer sim.mutex.Unlock()

	if !sim.running {
		return nil
	}

	if sim.server != nil {
		if err := sim.server.Stop(); err != nil {
			logger.Error("停止Modbus服务器失败: %v", err)
		}
		sim.server = nil
	}

	sim.running = false
	logger.Info("逆变器模拟器 [表位%d] 已停止", sim.position)

	return nil
}

// IsRunning 检查是否正在运行
func (sim *InverterSimulator) IsRunning() bool {
	sim.mutex.RLock()
	defer sim.mutex.RUnlock()
	return sim.running
}

// GetPosition 获取表位号
func (sim *InverterSimulator) GetPosition() int32 {
	return sim.position
}

// ReadInputRegister 读取输入寄存器
func (sim *InverterSimulator) ReadInputRegister(address uint16) (uint16, error) {
	sim.mutex.RLock()
	defer sim.mutex.RUnlock()

	if !sim.config.IsAddressInRange(address, 4) {
		return 0, fmt.Errorf("地址 %d 超出输入寄存器范围", address)
	}

	value, exists := sim.inputRegs[address]
	if !exists {
		return 0, nil // 返回默认值0
	}

	return value, nil
}

// ReadHoldingRegister 读取保持寄存器
func (sim *InverterSimulator) ReadHoldingRegister(address uint16) (uint16, error) {
	sim.mutex.RLock()
	defer sim.mutex.RUnlock()

	if !sim.config.IsAddressInRange(address, 3) {
		return 0, fmt.Errorf("地址 %d 超出保持寄存器范围", address)
	}

	value, exists := sim.holdingRegs[address]
	if !exists {
		return 0, nil // 返回默认值0
	}

	return value, nil
}

// WriteHoldingRegister 写入保持寄存器
func (sim *InverterSimulator) WriteHoldingRegister(address uint16, value uint16) error {
	sim.mutex.Lock()
	defer sim.mutex.Unlock()

	if !sim.config.IsAddressInRange(address, 3) {
		return fmt.Errorf("地址 %d 超出保持寄存器范围", address)
	}

	sim.holdingRegs[address] = value
	logger.Debug("写入保持寄存器 [表位%d] 地址: %d, 值: %d", sim.position, address, value)

	return nil
}

// WriteInputRegister 写入输入寄存器 (用于模拟数据更新)
func (sim *InverterSimulator) WriteInputRegister(address uint16, value uint16) error {
	sim.mutex.Lock()
	defer sim.mutex.Unlock()

	if !sim.config.IsAddressInRange(address, 4) {
		return fmt.Errorf("地址 %d 超出输入寄存器范围", address)
	}

	sim.inputRegs[address] = value
	logger.Debug("更新输入寄存器 [表位%d] 地址: %d, 值: %d", sim.position, address, value)

	return nil
}

// GetRegisterValue 获取寄存器值 (通用接口)
func (sim *InverterSimulator) GetRegisterValue(funcCode uint8, address uint16) (uint16, error) {
	switch funcCode {
	case 3: // 保持寄存器
		return sim.ReadHoldingRegister(address)
	case 4: // 输入寄存器
		return sim.ReadInputRegister(address)
	default:
		return 0, fmt.Errorf("不支持的功能码: %d", funcCode)
	}
}

// SetRegisterValue 设置寄存器值 (通用接口)
func (sim *InverterSimulator) SetRegisterValue(funcCode uint8, address uint16, value uint16) error {
	switch funcCode {
	case 3: // 保持寄存器
		return sim.WriteHoldingRegister(address, value)
	case 4: // 输入寄存器 (用于模拟)
		return sim.WriteInputRegister(address, value)
	default:
		return fmt.Errorf("不支持的功能码: %d", funcCode)
	}
}

// updateData 定期更新数据 (模拟实时数据变化)
func (sim *InverterSimulator) updateData() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !sim.IsRunning() {
				return
			}
			sim.simulateDataChanges()
		}
	}
}

// simulateDataChanges 模拟数据变化
func (sim *InverterSimulator) simulateDataChanges() {
	sim.mutex.Lock()
	defer sim.mutex.Unlock()

	// 模拟一些动态数据变化，比如电压、电流、功率等
	now := time.Now()

	// 模拟电压波动 (假设基准电压是2200，波动范围±50)
	for address, regInfo := range sim.config.RegisterData {
		if regInfo.FunctionCode == 2 { // 输入寄存器
			// 根据描述判断是否需要模拟变化
			desc := regInfo.Description
			if strings.Contains(desc, "电压") || strings.Contains(desc, "voltage") {
				baseValue := regInfo.Value
				variation := int32(50 * (0.5 - float64(now.Unix()%100)/100.0)) // ±50的波动
				newValue := uint16(int32(baseValue) + variation)
				if newValue > 0 {
					sim.inputRegs[address] = newValue
				}
			} else if strings.Contains(desc, "电流") || strings.Contains(desc, "current") {
				baseValue := regInfo.Value
				variation := int32(20 * (0.5 - float64(now.Unix()%80)/80.0)) // ±20的波动
				newValue := uint16(int32(baseValue) + variation)
				if newValue > 0 {
					sim.inputRegs[address] = newValue
				}
			}
		}
	}
}

// handleModbusRequest 处理Modbus请求
func (sim *InverterSimulator) handleModbusRequest(request *ModbusRequest) *ModbusResponse {
	response := &ModbusResponse{
		SlaveID:      request.SlaveID,
		FunctionCode: request.FunctionCode,
	}

	switch request.FunctionCode {
	case FuncReadHoldingRegisters:
		return sim.handleReadHoldingRegisters(request, response)
	case FuncReadInputRegisters:
		return sim.handleReadInputRegisters(request, response)
	case FuncWriteSingleRegister:
		return sim.handleWriteSingleRegister(request, response)
	case FuncWriteMultipleRegisters:
		return sim.handleWriteMultipleRegisters(request, response)
	default:
		response.Exception = ExceptionIllegalFunction
		return response
	}
}

// handleReadHoldingRegisters 处理读保持寄存器请求
func (sim *InverterSimulator) handleReadHoldingRegisters(request *ModbusRequest, response *ModbusResponse) *ModbusResponse {
	if len(request.Data) < 4 {
		response.Exception = ExceptionIllegalDataValue
		return response
	}

	startAddr := (uint16(request.Data[0]) << 8) | uint16(request.Data[1])
	quantity := (uint16(request.Data[2]) << 8) | uint16(request.Data[3])

	if quantity == 0 || quantity > 125 {
		response.Exception = ExceptionIllegalDataValue
		return response
	}

	// 检查地址范围
	if !sim.config.IsAddressInRange(startAddr, 3) || !sim.config.IsAddressInRange(startAddr+quantity-1, 3) {
		response.Exception = ExceptionIllegalDataAddress
		return response
	}

	// 读取数据
	data := make([]byte, 1+quantity*2)
	data[0] = byte(quantity * 2) // 字节数

	sim.mutex.RLock()
	for i := uint16(0); i < quantity; i++ {
		addr := startAddr + i
		value, exists := sim.holdingRegs[addr]
		if !exists {
			value = 0 // 默认值
		}
		data[1+i*2] = byte(value >> 8)     // 高字节
		data[1+i*2+1] = byte(value & 0xFF) // 低字节
	}
	sim.mutex.RUnlock()

	response.Data = data
	logger.Debug("读保持寄存器 [表位%d] 起始地址: %d, 数量: %d", sim.position, startAddr, quantity)
	return response
}

// handleReadInputRegisters 处理读输入寄存器请求
func (sim *InverterSimulator) handleReadInputRegisters(request *ModbusRequest, response *ModbusResponse) *ModbusResponse {
	if len(request.Data) < 4 {
		response.Exception = ExceptionIllegalDataValue
		return response
	}

	startAddr := (uint16(request.Data[0]) << 8) | uint16(request.Data[1])
	quantity := (uint16(request.Data[2]) << 8) | uint16(request.Data[3])

	if quantity == 0 || quantity > 125 {
		response.Exception = ExceptionIllegalDataValue
		return response
	}

	// 检查地址范围
	if !sim.config.IsAddressInRange(startAddr, 4) || !sim.config.IsAddressInRange(startAddr+quantity-1, 4) {
		response.Exception = ExceptionIllegalDataAddress
		return response
	}

	// 读取数据
	data := make([]byte, 1+quantity*2)
	data[0] = byte(quantity * 2) // 字节数

	sim.mutex.RLock()
	for i := uint16(0); i < quantity; i++ {
		addr := startAddr + i
		value, exists := sim.inputRegs[addr]
		if !exists {
			value = 0 // 默认值
		}
		data[1+i*2] = byte(value >> 8)     // 高字节
		data[1+i*2+1] = byte(value & 0xFF) // 低字节
	}
	sim.mutex.RUnlock()

	response.Data = data
	logger.Debug("读输入寄存器 [表位%d] 起始地址: %d, 数量: %d", sim.position, startAddr, quantity)
	return response
}

// handleWriteSingleRegister 处理写单个寄存器请求
func (sim *InverterSimulator) handleWriteSingleRegister(request *ModbusRequest, response *ModbusResponse) *ModbusResponse {
	if len(request.Data) < 4 {
		response.Exception = ExceptionIllegalDataValue
		return response
	}

	addr := (uint16(request.Data[0]) << 8) | uint16(request.Data[1])
	value := (uint16(request.Data[2]) << 8) | uint16(request.Data[3])

	// 检查地址范围
	if !sim.config.IsAddressInRange(addr, 3) {
		response.Exception = ExceptionIllegalDataAddress
		return response
	}

	// 写入数据
	sim.mutex.Lock()
	sim.holdingRegs[addr] = value
	sim.mutex.Unlock()

	// 响应数据与请求数据相同
	response.Data = request.Data
	logger.Debug("写单个寄存器 [表位%d] 地址: %d, 值: %d", sim.position, addr, value)
	return response
}

// handleWriteMultipleRegisters 处理写多个寄存器请求
func (sim *InverterSimulator) handleWriteMultipleRegisters(request *ModbusRequest, response *ModbusResponse) *ModbusResponse {
	if len(request.Data) < 5 {
		response.Exception = ExceptionIllegalDataValue
		return response
	}

	startAddr := (uint16(request.Data[0]) << 8) | uint16(request.Data[1])
	quantity := (uint16(request.Data[2]) << 8) | uint16(request.Data[3])
	byteCount := request.Data[4]

	if quantity == 0 || quantity > 123 || byteCount != byte(quantity*2) {
		response.Exception = ExceptionIllegalDataValue
		return response
	}

	if len(request.Data) < int(5+byteCount) {
		response.Exception = ExceptionIllegalDataValue
		return response
	}

	// 检查地址范围
	if !sim.config.IsAddressInRange(startAddr, 3) || !sim.config.IsAddressInRange(startAddr+quantity-1, 3) {
		response.Exception = ExceptionIllegalDataAddress
		return response
	}

	// 写入数据
	sim.mutex.Lock()
	for i := uint16(0); i < quantity; i++ {
		addr := startAddr + i
		value := (uint16(request.Data[5+i*2]) << 8) | uint16(request.Data[5+i*2+1])
		sim.holdingRegs[addr] = value
	}
	sim.mutex.Unlock()

	// 响应数据：起始地址 + 寄存器数量
	response.Data = request.Data[:4]
	logger.Debug("写多个寄存器 [表位%d] 起始地址: %d, 数量: %d", sim.position, startAddr, quantity)
	return response
}
