package dlt698

import (
	"bytes"
	"encoding/binary"
	"errors"
	"fmt"
)

// Parser 表示DL/T 698.45协议解析器
type Parser struct {
	buffer bytes.Buffer
}

// NewParser 创建一个新的解析器
func NewParser() *Parser {
	return &Parser{}
}

// Append 向解析器添加数据
func (p *Parser) Append(data []byte) {
	p.buffer.Write(data)
}

// Reset 重置解析器
func (p *Parser) Reset() {
	p.buffer.Reset()
}

// Available 返回可用数据长度
func (p *Parser) Available() int {
	return p.buffer.Len()
}

// ParseFrame 解析一个完整的帧
// 如果数据不足或无效，返回nil和错误
func (p *Parser) ParseFrame() (*Frame, error) {
	// 检查数据是否足够
	data := p.buffer.Bytes()
	if len(data) < 7 { // 最小帧长度
		return nil, errors.New("数据不足，无法解析帧")
	}

	// 查找帧起始符
	startIndex := bytes.IndexByte(data, FrameFormatNormal)
	if startIndex == -1 {
		// 没有找到起始符，清空缓冲区
		p.buffer.Reset()
		return nil, errors.New("未找到帧起始符")
	}

	// 如果起始符不在开头，丢弃之前的数据
	if startIndex > 0 {
		p.buffer.Next(startIndex)
		data = p.buffer.Bytes()
	}

	// 再次检查数据是否足够
	if len(data) < 7 {
		return nil, errors.New("数据不足，无法解析帧")
	}

	// 解析长度域
	length := binary.LittleEndian.Uint16(data[1:3])

	// 计算完整帧的长度
	frameLength := int(length) + 6 // 起始符(1) + 长度域(2) + 数据(length) + 校验和(2) + 结束符(1)

	// 检查数据是否足够
	if len(data) < frameLength {
		return nil, errors.New("数据不足，无法解析完整帧")
	}

	// 检查结束符
	if data[frameLength-1] != FrameFormatEnd {
		// 结束符错误，丢弃当前帧
		p.buffer.Next(1) // 只丢弃起始符，继续查找下一个起始符
		return nil, errors.New("帧结束符错误")
	}

	// 提取完整帧数据
	frameData := make([]byte, frameLength)
	copy(frameData, data[:frameLength])

	// 从缓冲区中移除已处理的数据
	p.buffer.Next(frameLength)

	// 解析帧
	return Decode(frameData)
}

// ParseGetResponseNormal 解析正常读取响应
func ParseGetResponseNormal(apdu *APDU) (map[string]interface{}, error) {
	if apdu.Type != byte(APDUTypeGetResp) {
		return nil, fmt.Errorf("APDU类型错误，期望0x%02X，实际0x%02X", APDUTypeGetResp, apdu.Type)
	}

	if len(apdu.Data) < 2 {
		return nil, errors.New("数据长度不足，无法解析读取响应")
	}

	// 解析PIID-ACD
	piidAcd := apdu.Data[0]
	_ = piidAcd // 暂不使用

	// 解析DAR（数据访问结果）
	dar := apdu.Data[1]
	if dar != 0 {
		return map[string]interface{}{
			"result":  "error",
			"code":    dar,
			"message": getErrorMessage(dar),
		}, nil
	}

	// 解析数据
	if len(apdu.Data) < 3 {
		return nil, errors.New("数据长度不足，无法解析读取响应数据")
	}

	// 解析数据项数量
	count := int(apdu.Data[2])
	result := make(map[string]interface{})
	result["result"] = "success"
	result["count"] = count

	// 解析数据项
	items := make([]map[string]interface{}, 0, count)
	offset := 3

	for i := 0; i < count; i++ {
		if offset+4 > len(apdu.Data) {
			return nil, errors.New("数据长度不足，无法解析数据项")
		}

		// 解析OAD
		oad, err := DecodeOAD(apdu.Data[offset : offset+4])
		if err != nil {
			return nil, err
		}
		offset += 4

		// 解析数据
		dataType := apdu.Data[offset]
		offset++

		// 解析具体数据
		value, newOffset, err := parseData(dataType, apdu.Data, offset)
		if err != nil {
			return nil, err
		}
		offset = newOffset

		// 添加到结果
		items = append(items, map[string]interface{}{
			"oi":     fmt.Sprintf("0x%04X", oad.OI),
			"attrib": oad.AttributeID,
			"index":  oad.AttributeIndex,
			"type":   dataType,
			"value":  value,
		})
	}

	result["items"] = items
	return result, nil
}

// ParseSetResponseNormal 解析正常设置响应
func ParseSetResponseNormal(apdu *APDU) (map[string]interface{}, error) {
	if apdu.Type != byte(APDUTypeSetResp) {
		return nil, fmt.Errorf("APDU类型错误，期望0x%02X，实际0x%02X", APDUTypeSetResp, apdu.Type)
	}

	if len(apdu.Data) < 2 {
		return nil, errors.New("数据长度不足，无法解析设置响应")
	}

	// 解析PIID-ACD
	piidAcd := apdu.Data[0]
	_ = piidAcd // 暂不使用

	// 解析DAR（数据访问结果）
	dar := apdu.Data[1]
	if dar != 0 {
		return map[string]interface{}{
			"result":  "error",
			"code":    dar,
			"message": getErrorMessage(dar),
		}, nil
	}

	return map[string]interface{}{
		"result": "success",
	}, nil
}

// ParseActionResponseNormal 解析正常操作响应
func ParseActionResponseNormal(apdu *APDU) (map[string]interface{}, error) {
	if apdu.Type != byte(APDUTypeActionResp) {
		return nil, fmt.Errorf("APDU类型错误，期望0x%02X，实际0x%02X", APDUTypeActionResp, apdu.Type)
	}

	if len(apdu.Data) < 2 {
		return nil, errors.New("数据长度不足，无法解析操作响应")
	}

	// 解析PIID-ACD
	piidAcd := apdu.Data[0]
	_ = piidAcd // 暂不使用

	// 解析DAR（数据访问结果）
	dar := apdu.Data[1]
	if dar != 0 {
		return map[string]interface{}{
			"result":  "error",
			"code":    dar,
			"message": getErrorMessage(dar),
		}, nil
	}

	// 解析数据
	if len(apdu.Data) < 3 {
		return map[string]interface{}{
			"result": "success",
			"data":   nil,
		}, nil
	}

	// 解析数据类型
	dataType := apdu.Data[2]

	// 解析具体数据
	value, _, err := parseData(dataType, apdu.Data, 3)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"result": "success",
		"type":   dataType,
		"data":   value,
	}, nil
}

// parseData 解析数据
func parseData(dataType byte, data []byte, offset int) (interface{}, int, error) {
	if offset >= len(data) {
		return nil, offset, errors.New("数据长度不足")
	}

	switch dataType {
	case DataTypeNull:
		return nil, offset, nil
	case DataTypeBool:
		if offset >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		return data[offset] != 0, offset + 1, nil
	case DataTypeUnsigned, DataTypeEnum:
		if offset >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		return uint8(data[offset]), offset + 1, nil
	case DataTypeInteger:
		if offset >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		return int8(data[offset]), offset + 1, nil
	case DataTypeUnsigned16:
		if offset+1 >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		return binary.LittleEndian.Uint16(data[offset : offset+2]), offset + 2, nil
	case DataTypeInteger16:
		if offset+1 >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		return int16(binary.LittleEndian.Uint16(data[offset : offset+2])), offset + 2, nil
	case DataTypeUnsigned32:
		if offset+3 >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		return binary.LittleEndian.Uint32(data[offset : offset+4]), offset + 4, nil
	case DataTypeInteger32:
		if offset+3 >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		return int32(binary.LittleEndian.Uint32(data[offset : offset+4])), offset + 4, nil
	case DataTypeOctetString, DataTypeVisibleString, DataTypeUTF8String:
		if offset >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		length := int(data[offset])
		offset++
		if offset+length > len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		return string(data[offset : offset+length]), offset + length, nil
	case DataTypeDateTime:
		if offset+6 >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		dt := &DateTime{
			Year:         LongUnsigned(binary.LittleEndian.Uint16(data[offset : offset+2])),
			Month:        Unsigned(data[offset+2]),
			DayOfMonth:   Unsigned(data[offset+3]),
			DayOfWeek:    Unsigned(data[offset+4]),
			Hour:         Unsigned(data[offset+5]),
			Minute:       Unsigned(data[offset+6]),
			Second:       Unsigned(data[offset+7]),
			Milliseconds: LongUnsigned(binary.LittleEndian.Uint16(data[offset+8 : offset+10])),
		}
		return dt, offset + 7, nil
	case DataTypeDataTimeS:
		if offset+6 >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		dt := &DateTimeS{
			Year:   LongUnsigned(binary.LittleEndian.Uint16(data[offset : offset+2])),
			Month:  Unsigned(data[offset+2]),
			Day:    Unsigned(data[offset+3]),
			Hour:   Unsigned(data[offset+4]),
			Minute: Unsigned(data[offset+5]),
			Second: Unsigned(data[offset+6]),
		}
		return dt.ToTime().Format("2006-01-02 15:04:05"), offset + 7, nil
	case DataTypeArray:
		if offset >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		count := int(data[offset])
		offset++

		array := make([]interface{}, 0, count)
		for i := 0; i < count; i++ {
			if offset >= len(data) {
				return nil, offset, errors.New("数据长度不足")
			}
			elementType := data[offset]
			offset++

			value, newOffset, err := parseData(elementType, data, offset)
			if err != nil {
				return nil, offset, err
			}
			offset = newOffset
			array = append(array, value)
		}
		return array, offset, nil
	case DataTypeStruct:
		if offset >= len(data) {
			return nil, offset, errors.New("数据长度不足")
		}
		count := int(data[offset])
		offset++

		structure := make(map[string]interface{})
		for i := 0; i < count; i++ {
			if offset >= len(data) {
				return nil, offset, errors.New("数据长度不足")
			}
			elementType := data[offset]
			offset++

			value, newOffset, err := parseData(elementType, data, offset)
			if err != nil {
				return nil, offset, err
			}
			offset = newOffset
			structure[fmt.Sprintf("field_%d", i)] = value
		}
		return structure, offset, nil
	default:
		return nil, offset, fmt.Errorf("不支持的数据类型: 0x%02X", dataType)
	}
}

// getErrorMessage 获取错误消息
func getErrorMessage(errorCode byte) string {
	switch errorCode {
	case ErrorSuccess:
		return "成功"
	case ErrorHardwareFault:
		return "硬件失效"
	case ErrorTemporaryFault:
		return "暂时失效"
	case ErrorReadWriteDenied:
		return "拒绝读写"
	case ErrorObjectUndefined:
		return "对象未定义"
	case ErrorObjectClassInconsistent:
		return "对象接口类不一致"
	case ErrorObjectUnavailable:
		return "对象不存在"
	case ErrorTypeUnmatched:
		return "类型不匹配"
	case ErrorScopeOutOfBounds:
		return "越界"
	case ErrorDataOutOfBounds:
		return "数据块不可用"
	case ErrorObjectMethodUnmatched:
		return "对象方法不匹配"
	case ErrorObjectMethodUnavailable:
		return "对象方法不可用"
	case ErrorOtherReason:
		return "其他原因"
	default:
		return fmt.Sprintf("未知错误(0x%02X)", errorCode)
	}
}
