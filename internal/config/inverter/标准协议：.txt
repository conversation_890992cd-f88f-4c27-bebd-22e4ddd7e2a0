[bi<PERSON><PERSON>hun]
ACTIVE_POWER_CONTROL= [(2, 61698, 2, "有功功率控制"), ]
ACTIVE_POWER_PERCENTAGE = [(2, 61700, 1, "有功功率百分比"), ]
REACTIVE_POWER_CONTROL= [(2, 61701, 2, "无功功率控制"), ]
REACTIVE_POWER_PERCENTAGE = [(2, 61703, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 61704, 1, "功率因数"), ]
VOLTAGE_L1 = [(3, 61973, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 61974, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 61975, 1, "L3 电压"), ]
CURRENT_A = [(3, 61976, 1, "A 相电流"), ]
CURRENT_B = [(3, 61977, 1, "B 相电流"), ]
CURRENT_C = [(3, 61978, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 61697, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [(3, 61520, 2, "额定有功"), ]
RATED_REACTIVE_POWER = [(3, 61522, 2, "额定无功"), ]
OUTPUT_TYPE = [(3, 61524, 1, "输出类型"), ]
SERIAL_NUMBER = [(3, 61440, 10, "序列号"), ]
OPERATIONAL_STATUS = [( ), ]
ACTIVE_POWER = [(3, 61985, 2, "有功功率"), ]
REACTIVE_POWER = [(3, 61993, 2, "无功功率"),]

[jinlang]
ACTIVE_POWER_CONTROL = [(2, 3080, 1, "有功功率控制"), ]
ACTIVE_POWER_PERCENTAGE = [(2,3051, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [(2, 3082, 1, "无功功率控制"), ]
REACTIVE_POWER_PERCENTAGE = [(2, 3050, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 3052, 1, "功率因数"), (2, 3053, 1, "功率因数")]
VOLTAGE_L1 = [(3, 3033, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 3034, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 3035, 1, "L3 电压"), ]
CURRENT_A = [(3, 3036, 1, "A 相电流"), ]
CURRENT_B = [(3, 3037, 1, "B 相电流"), ]
CURRENT_C = [(3, 3038, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 3006, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [(3, 3002, 1, "输出类型"), ]
SERIAL_NUMBER = [(3, 3060, 4, "序列号"), ]
OPERATIONAL_STATUS = [(3, 3043, 1, "运行状态"), ]
ACTIVE_POWER = [(3, 3004, 2, "有功功率"), ]
REACTIVE_POWER = [(3, 3055, 2, "无功功率"),]


[aishiwei]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2,5402, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [( ), ]
POWER_FACTOR = [(2, 5503, 1, "功率因数"), ]
VOLTAGE_L1 = [(3, 1358, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 1360, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 1362, 1, "L3 电压"), ]
CURRENT_A = [(3, 1359, 1, "A 相电流"), ]
CURRENT_B = [(3, 1361, 1, "B 相电流"), ]
CURRENT_C = [(3, 1363, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 200, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [(3, 1027, 1, "额定有功"), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [(3, 1000, 1, "输出类型"), ]
SERIAL_NUMBER = [(3, 1002, 16, "序列号"), ]
OPERATIONAL_STATUS = [(3, 1308, 1, "运行状态"), ]
ACTIVE_POWER = [(3, 1370, 2, "有功功率"), ]
REACTIVE_POWER = [(3, 1372, 2, "无功功率"),]

[gudewei]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2, 256, 1, "有功功率百分比"), ]
REACTIVE_POWER_CONTROL = [(2, 258, 2, "无功功率控制值"), ]
REACTIVE_POWER_PERCENTAGE = [(2, 257, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 886, 1, "功率因数"), ]
VOLTAGE_L1 = [(2, 554, 1, "L1 电压"), (2, 772, 1, "L1 电压")]
VOLTAGE_L2 = [(2, 555, 1, "L2 电压"), (2, 773, 1, "L2 电压")]
VOLTAGE_L3 = [(2, 556, 1, "L3 电压"), (2, 774, 1, "L3 电压")]
CURRENT_A = [(2, 557, 1, "A 相电流"), (2, 775, 1, "A 相电流")]
CURRENT_B = [(2, 558, 1, "B 相电流"), (2, 776, 1, "B 相电流")]
CURRENT_C = [(2, 559, 1, "C 相电流"), (2, 777, 1, "C 相电流")]
INVERTER_SWITCH = [(2, 288, 1, "逆变器开"), (2, 289, 1, "逆变器关")]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 512, 8, "序列号"), ]
OPERATIONAL_STATUS = [(2, 564, 1, "运行状态"), (2, 782, 1, "运行状态")]
ACTIVE_POWER = [( ), ]
REACTIVE_POWER = [( ),]

[huawei1]
ACTIVE_POWER_CONTROL = [(2, 35301, 2, "有功功率控制值"), ]
ACTIVE_POWER_PERCENTAGE = [(2,35301, 2, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [(2, 35305, 1, "无功功率控制值"), ]
REACTIVE_POWER_PERCENTAGE = [(2, 35305, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 32084, 1, "功率因数"), ]
VOLTAGE_L1 = [(2, 32069, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 32070, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 32071, 1, "L3 电压"), ]
CURRENT_A = [(2, 32072, 2, "A 相电流"), ]
CURRENT_B = [(2, 32074, 2, "B 相电流"), ]
CURRENT_C = [(2, 32076, 2, "C 相电流"), ]
INVERTER_SWITCH = [(2, 40200, 1, "逆变器开"), (2, 40201, 1"逆变器关") ]
RATED_ACTIVE_POWER = [(2, 30073, 1, "额定有功"), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 30015, 10, "序列号"), ]
OPERATIONAL_STATUS = [(2, 32089, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 32080, 2, "有功功率" ), ]
REACTIVE_POWER = [(2, 32082, 2, "无功功率" ),]

[sanjingplus]
ACTIVE_POWER_CONTROL  = [( ), ]
ACTIVE_POWER_PERCENTAGE = [( ),]
REACTIVE_POWER_CONTROL  = [( ), ]
REACTIVE_POWER_PERCENTAGE = [( ), ]
POWER_FACTOR = [( ), ]
VOLTAGE_L1 = [(2, 278, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 284, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 290, 1, "L3 电压"), ]
CURRENT_A = [(2, 279, 1, "A 相电流"), ]
CURRENT_B = [(2, 285, 1, "B 相电流"), ]
CURRENT_C = [(2, 291, 1, "C 相电流"), ]
INVERTER_SWITCH = [( ), ]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [(2, 36608, 1, "输出类型"), ]
SERIAL_NUMBER = [(2, 36611, 10, "序列号"), ]
OPERATIONAL_STATUS = [(2, 256, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 275, 1, "有功功率" ), ]
REACTIVE_POWER = [(2, 276, 1, "无功功率" ),]

[sanjingr6]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [( ),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [( ), ]
POWER_FACTOR = [( ), ]
VOLTAGE_L1 = [(2, 24610, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 24616, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 24622, 1, "L3 电压"), ]
CURRENT_A = [(2, 24611, 1, "A 相电流"), ]
CURRENT_B = [(2, 24617, 1, "B 相电流"), ]
CURRENT_C = [(2, 24623, 1, "C 相电流"), ]
INVERTER_SWITCH = [( ), ]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [(2, 36608, 1, "输出类型"), ]
SERIAL_NUMBER = [(2, 36611, 10, "序列号"), ]
OPERATIONAL_STATUS = [( ), ]
ACTIVE_POWER = [(2, 24605, 2, "有功功率" ), ]
REACTIVE_POWER = [(2, 24607, 2, "无功功率" ),]

[yangguang]
ACTIVE_POWER_CONTROL = [(2, 5038, 1, "有功功率控制值"), ]
ACTIVE_POWER_PERCENTAGE = [(2,5007, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [(2, 5039, 1, "无功功率控制值"), ]
REACTIVE_POWER_PERCENTAGE = [(2, 5036, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 5018, 1, "功率因数"), ]
VOLTAGE_L1 = [(3, 5018, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 5019, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 5020, 1, "L3 电压"), ]
CURRENT_A = [(3, 5021, 1, "A 相电流"), ]
CURRENT_B = [(3, 5022, 1, "B 相电流"), ]
CURRENT_C = [(3, 5023, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 5005, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [(3, 5000, 1, "额定有功"), ]
RATED_REACTIVE_POWER = [(3, 5048, 1, "额定无功"), ]
OUTPUT_TYPE = [(3, 5001, 1, "输出类型"), ]
SERIAL_NUMBER = [(3, 4989, 10, "序列号"), ]
OPERATIONAL_STATUS = [(3, 5037, 1, "运行状态"), ]
ACTIVE_POWER = [(3, 5030, 2, "有功功率" ), ]
REACTIVE_POWER = [(3, 5032, 2, "无功功率" ),]

[guruiwate1]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2, 3, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [(2, 4, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 5, 1, "功率因数"), ]
VOLTAGE_L1 = [(3, 4110, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 4114, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 4118, 1, "L3 电压"), ]
CURRENT_A = [(3, 4111, 1, "A 相电流"), ]
CURRENT_B = [(3, 4115, 1, "B 相电流"), ]
CURRENT_C = [(3, 4119, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 0, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [(2, 6, 2, "额定有功"), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 23, 5, "序列号"), ]
OPERATIONAL_STATUS = [(3, 4096, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 147, 2, "有功功率" ), ]
REACTIVE_POWER = [( ),]

[guruiwate2]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2,3, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [(2, 137, 2, "无功功率"),  ]
REACTIVE_POWER_PERCENTAGE = [(2, 4, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 5, 1, "功率因数"), ]
VOLTAGE_L1 = [(3, 38, 1, "L1 电压"),(3, 3026, 1, "L1 电压") ]
VOLTAGE_L2 = [(3, 42, 1, "L2 电压"),(3, 3030, 1, "L2 电压") ]
VOLTAGE_L3 = [(3, 46, 1, "L3 电压"),(3, 3034, 1, "L3 电压") ]
CURRENT_A = [(3, 39, 1, "A 相电流"),(3, 3027, 1, "A 相电流") ]
CURRENT_B = [(3, 43, 1, "B 相电流"),(3, 3031, 1, "B 相电流") ]
CURRENT_C = [(3, 47, 1, "C 相电流"),(3, 3035, 1, "C 相电流") ]
INVERTER_SWITCH = [(2, 0, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [(2, 6, 2, "额定有功"), ]
RATED_REACTIVE_POWER = [(2, 3082, 1, "额定无功"), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 5333, 5, "序列号"), (2, 7218, 5, "序列号"), (2, 7482, 15, "序列号")]
OPERATIONAL_STATUS = [(3, 0, 1, "运行状态"), (3, 141, 1, "运行状态")]
ACTIVE_POWER = [(3, 35, 2, "有功功率" ), ]
REACTIVE_POWER = [(3, 232, 2, "无功功率" ),]

[cpssca]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2,20740, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [(2, 20756, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 20529, 1, "功率因数"), ]
VOLTAGE_L1 = [(2, 4097, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 4102, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 4107, 1, "L3 电压"), ]
CURRENT_A = [(2, 4098, 1, "A 相电流"), ]
CURRENT_B = [(2, 4103, 1, "B 相电流"), ]
CURRENT_C = [(2, 4108, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 24577, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [(2, 6726, 1, "额定有功"), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [(2, 6728, 1, "输出类型"), ]
SERIAL_NUMBER = [(2, 6672, 8, "序列号"), ]
OPERATIONAL_STATUS = [(2, 4125, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 4151, 2, "有功功率" ), ]
REACTIVE_POWER = [(2, 4153, 2, "无功功率" ),]

[zhengtai]
ACTIVE_POWER_CONTROL  = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2,4097, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL  = [( ), ]
REACTIVE_POWER_PERCENTAGE = [(2, 4099, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 4098, 1, "功率因数"), ]
VOLTAGE_L1 = [(3, 31, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 32, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 33, 1, "L3 电压"), ]
CURRENT_A = [(3, 34, 1, "A 相电流"), ]
CURRENT_B = [(3, 35, 1, "B 相电流"), ]
CURRENT_C = [(3, 36, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 4096, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(3, 6, 4, "序列号"), ]
OPERATIONAL_STATUS = [(3, 47, 1, "运行状态"), ]
ACTIVE_POWER = [(3, 29, 1, "有功功率" ), ]
REACTIVE_POWER = [(2, 59, 1, "无功功率" ),]

[shouhang1]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2,4358, 1, "有功功率百分比" ),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [( 2, 4360, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 4361, 1, "功率因数"), ]
VOLTAGE_L1 = [(2, 1737, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 1738, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 1739, 1, "L3 电压"), ]
CURRENT_A = [(2, 1734, 1, "A 相电流"), ]
CURRENT_B = [(2, 1735, 1, "B 相电流"), ]
CURRENT_C = [(2, 1736, 1, "C 相电流"), ]
INVERTER_SWITCH = [( 2, 4356, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [(2, 1773, 1, "额定有功"), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [(2, 20643, 1, "输出类型" ), ]
SERIAL_NUMBER = [(2, 1093, 8, "序列号"), ]
OPERATIONAL_STATUS = [(2, 1028, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 1157, 1, "有功功率" ), ]
REACTIVE_POWER = [(2, 1158, 1, "无功功率" ),]

[shouhang2]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2,4161, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [(2, 4194, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 4193, 1, "功率因数")]
VOLTAGE_L1 = [(3, 15, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 17, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 19, 1, "L3 电压"), ]
CURRENT_A = [(3, 16, 1, "A 相电流"), ]
CURRENT_B = [(3, 18, 1, "B 相电流"), ]
CURRENT_C = [(3, 20, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 4162, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(3, 8193, 7, "序列号"), ]
OPERATIONAL_STATUS = [(2, 0, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 12, 1, "有功功率" ), ]
REACTIVE_POWER = [(2, 13,1, "无功功率" ),]

[hewang1]
ACTIVE_POWER_CONTROL = [(2, 40012, 1, "有功功率"), ]
ACTIVE_POWER_PERCENTAGE = [(2,40013, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL  = [(2, 40004, 1, "无功功率"), ]
REACTIVE_POWER_PERCENTAGE = [(2, 40005, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 40003, 1, "功率因数")]
VOLTAGE_L1 = [(3, 40532, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 40533, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 40534, 1, "L3 电压"), ]
CURRENT_A = [(3, 40535, 1, "A 相电流"), ]
CURRENT_B = [(3, 40536, 1, "B 相电流"), ]
CURRENT_C = [(3, 40537, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 40200, 1, "逆变器开机"), (2, 40201, 1, "逆变器关机")]
RATED_ACTIVE_POWER = [(2, 40646, 1, "额定有功"), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [( ), ]
OPERATIONAL_STATUS = [(3, 40547, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 40539, 1, "有功功率" ), ]
REACTIVE_POWER = [(2, 40540, 1, "无功功率" ),]

[hewang2]
ACTIVE_POWER_CONTROL = [(2, 40012, 1, "有功功率"), ]
ACTIVE_POWER_PERCENTAGE = [(2,40013, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [(2, 40004, 1, "无功功率"), ]
REACTIVE_POWER_PERCENTAGE = [(2, 40005, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 40003, 1, "功率因数")]
VOLTAGE_L1 = [(3, 40364, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 40365, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 40366, 1, "L3 电压"), ]
CURRENT_A = [(3, 40367, 1, "A 相电流"), ]
CURRENT_B = [(3, 40368, 1, "B 相电流"), ]
CURRENT_C = [(3, 40369, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 40200, 1, "逆变器开机"), (2, 40201, 1, "逆变器关机")]
RATED_ACTIVE_POWER = [(2, 40646, 1, "额定有功"), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [( ), ]
OPERATIONAL_STATUS = [(3, 40379, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 40371, 1, "有功功率" ), ]
REACTIVE_POWER = [(2, 40372, 1, "无功功率" ),]

[yingweiteng1]
ACTIVE_POWER_CONTROL = [(2, 12321, 1, "有功功率"), ]
ACTIVE_POWER_PERCENTAGE = [(2,12313, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [(2, 12316, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 12315, 1, "功率因数")]
VOLTAGE_L1 = [(2, 14356, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 14357, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 14358, 1, "L3 电压"), ]
CURRENT_A = [(2, 14359, 1, "A 相电流"), ]
CURRENT_B = [(2, 14360, 1, "B 相电流"), ]
CURRENT_C = [(2, 14361, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 12312, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [(2, 12288, 1, "额定有功"), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 12288, 16, "序列号"), ]
OPERATIONAL_STATUS = [(2, 14342, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 14365, 2, "有功功率" ), ]
REACTIVE_POWER = [(2, 14367, 2, "无功功率" ),]

[yingweiteng2]
ACTIVE_POWER_CONTROL = [(2, 4896, 1, "有功功率"), ]
ACTIVE_POWER_PERCENTAGE = [(2,4894, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [( ), ]
POWER_FACTOR = [(2, 4868, 1, "功率因数")]
VOLTAGE_L1 = [(2, 5784, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 5786, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 5788, 1, "L3 电压"), ]
CURRENT_A = [(2, 5785, 1, "A 相电流"), ]
CURRENT_B = [(2, 5787, 1, "B 相电流"), ]
CURRENT_C = [(2, 5789, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 5120, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 4632, 20, "序列号"), ]
OPERATIONAL_STATUS = [(2, 5756, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 8200, 2, "有功功率" ), ]
REACTIVE_POWER = [(2, 5774, 2, "无功功率" ),]

[maoshuodianqi]
ACTIVE_POWER_CONTROL = [(2, 49177, 1, "有功功率"), ]
ACTIVE_POWER_PERCENTAGE = [( ),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [( ), ]
POWER_FACTOR = [(2, 49178, 1, "功率因数"), ]
VOLTAGE_L1 = [(2, 49420, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 49422, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 49424, 1, "L3 电压"), ]
CURRENT_A = [(2, 49421, 1, "A 相电流"), ]
CURRENT_B = [(2, 49423, 1, "B 相电流"), ]
CURRENT_C = [(2, 49425, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 49180, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 49171, 4, "序列号"), ]
OPERATIONAL_STATUS = [(2, 49168, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 49438, 1, "有功功率" ), ]
REACTIVE_POWER = [(2, 49439, 1, "无功功率" ),]

[maigeruineng]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2,1220, 1, "有功功率百分比"),(2,1190, 1, "有功功率百分比")]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [(2, 1223, 1, "无功功率百分比"), (2, 1193, 1, "无功功率百分比")]
POWER_FACTOR = [(2, 1222, 1, "功率因数"), (2, 1192, 1, "功率因数")]
VOLTAGE_L1 = [(2, 1050, 1, "L1 电压"), (2, 1110, 1, "L1 电压")]
VOLTAGE_L2 = [(2, 1051, 1, "L2 电压"), (2, 1111, 1, "L2 电压")]
VOLTAGE_L3 = [(2, 1052, 1, "L3 电压"), (2, 1112, 1, "L3 电压")]
CURRENT_A = [(2, 1053, 1, "A 相电流"), (2, 1113, 1, "A 相电流")]
CURRENT_B = [(2, 1054, 1, "B 相电流"), (2, 1114, 1, "B 相电流")]
CURRENT_C = [(2, 1055, 1, "C 相电流"), (2, 1115, 1, "C 相电流")]
INVERTER_SWITCH = [(2, 1228, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 1320, 10, "序列号"), ]
OPERATIONAL_STATUS = [( ), ]
ACTIVE_POWER = [(2, 1118, 1, "有功功率" ), ]
REACTIVE_POWER = [(2, 1119, 1, "无功功率" ),]

[aotai]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2,41, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [(2, 42, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 117, 1, "功率因数"), ]
VOLTAGE_L1 = [(3, 10, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 11, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 12, 1, "L3 电压"), ]
CURRENT_A = [(3, 13, 1, "A 相电流"), ]
CURRENT_B = [(3, 14, 1, "B 相电流"), ]
CURRENT_C = [(3, 15, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 49, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 7, 1, "序列号"), ]
OPERATIONAL_STATUS = [(3, 0, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 17, 1, "有功功率" ), ]
REACTIVE_POWER = [(2, 18, 1, "无功功率" ),]

[huawei2]
ACTIVE_POWER_CONTROL = [(2, 40120, 1, "有功功率"), ]
ACTIVE_POWER_PERCENTAGE = [(2,40235, 1, "有功功率百分比"), (2,42320, 1, "有功功率百分比"), (2,40119, 1, "有功功率百分比")]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [(2, 40236, 1, "无功功率百分比"), (2, 42321, 1, "无功功率百分比")]
POWER_FACTOR = [(2, 40237, 1, "功率因数"), ]
VOLTAGE_L1 = [(2, 32277, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 32278, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 32279, 1, "L3 电压"), ]
CURRENT_A = [(2, 32280, 1, "A 相电流"), ]
CURRENT_B = [(2, 32281, 1, "B 相电流"), ]
CURRENT_C = [(2, 32282, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 40200, 1, "逆变器开机"), (2, 40201, 1, "逆变器关机")]
RATED_ACTIVE_POWER = [(2, 40120, 1, "额定有功"), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [(2, 32002, 1, "输出类型"), ]
SERIAL_NUMBER = [(2, 32003, 10, "序列号"), ]
OPERATIONAL_STATUS = [(2, 32287, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 32290, 2, "有功功率" ), ]
REACTIVE_POWER = [(2, 32292, 2, "无功功率" ),]

[huawei3]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2, 40119, 1, "有功功率百分比"), (2, 40234, 1, "有功功率百分比")]
REACTIVE_POWER_CONTROL = [(2, 40123, 1, "无功功率"), ]
REACTIVE_POWER_PERCENTAGE = [( ), ]
POWER_FACTOR = [(2, 40122, 1, "功率因数"), (2, 40237, 1, "功率因数")]
VOLTAGE_L1 = [(2, 40577, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 40578, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 40579, 1, "L3 电压"), ]
CURRENT_A = [(2, 40572, 1, "A 相电流"), ]
CURRENT_B = [(2, 40573, 1, "B 相电流"), ]
CURRENT_C = [(2, 40574, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 40200, 1, "逆变器开机"), (2, 40201, 1, "逆变器关机")]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 40713, 10, "序列号"), ]
OPERATIONAL_STATUS = [(2, 40939, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 40525, 2, "有功功率" ), ]
REACTIVE_POWER = [(2, 40544, 2, "无功功率" ),]

[keshida]
ACTIVE_POWER_CONTROL = [(3, 3050, 1, "有功功率"), ]
ACTIVE_POWER_PERCENTAGE = [(2,4004, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [(3, 3051, 1, "无功功率"), ]
REACTIVE_POWER_PERCENTAGE = [(2, 4005, 1, "无功功率百分比"), ]
POWER_FACTOR = [(3, 3049, 1, "功率因数"), (2, 4003, 1, "功率因数")]
VOLTAGE_L1 = [(3, 3014, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 3015, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 3016, 1, "L3 电压"), ]
CURRENT_A = [(3, 3020, 1, "A 相电流"), ]
CURRENT_B = [(3, 3021, 1, "B 相电流"), ]
CURRENT_C = [(3, 3022, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 4001, 1, "逆变器关机"), (2, 4002, 1, "逆变器开机")]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 3200, 5, "序列号"), ]
OPERATIONAL_STATUS = [(3, 3030, 1, "运行状态"), ]
ACTIVE_POWER = [(3, 3023, 2, "有功功率" ), ]
REACTIVE_POWER = [(3, 3054, 2, "无功功率" ),]

[deye]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2, 40, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [( ), ]
REACTIVE_POWER_PERCENTAGE = [(2, 40, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 39, 1, "功率因数"), ]
VOLTAGE_L1 = [(2, 73, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 74, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 75, 1, "L3 电压"), ]
CURRENT_A = [(2, 76, 1, "A 相电流"), ]
CURRENT_B = [(2, 77, 1, "B 相电流"), ]
CURRENT_C = [(2, 78, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 43, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [(2, 16, 2, "额定有功"), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(2, 3, 5, "序列号"), ]
OPERATIONAL_STATUS = [(2, 59, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 86, 2, "有功功率" ), ]
REACTIVE_POWER = [(2, 88, 2, "无功功率" ),]

[yinhenaiji]
ACTIVE_POWER_CONTROL = [(2, 6004, 1, "有功功率"), ]
ACTIVE_POWER_PERCENTAGE = [(2,6003, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [(2, 6007, 1, "无功功率"), ]
REACTIVE_POWER_PERCENTAGE = [(2, 6006, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 6008, 1, "功率因数"), ]
VOLTAGE_L1 = [(2, 3024, 1, "L1 电压"), ]
VOLTAGE_L2 = [(2, 3025, 1, "L2 电压"), ]
VOLTAGE_L3 = [(2, 3026, 1, "L3 电压"), ]
CURRENT_A = [(2, 3027, 1, "A 相电流"), ]
CURRENT_B = [(2, 3028, 1, "B 相电流"), ]
CURRENT_C = [(2, 3029, 1, "C 相电流"), ]
INVERTER_SWITCH = [(2, 6000, 1, "逆变器关机"), (2, 6001, 1, "逆变器开机")]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(3, 4027, 24, "序列号"), ]
OPERATIONAL_STATUS = [(2, 3057, 1, "运行状态"), ]
ACTIVE_POWER = [(2, 3033, 2, "有功功率" ), ]
REACTIVE_POWER = [(2, 3035, 2, "无功功率" ),]

[aifu]
ACTIVE_POWER_CONTROL = [(2, 1503, 1, "有功功率"), ]
ACTIVE_POWER_PERCENTAGE = [(2,1502, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [(2, 1551, 1, "无功功率"), ]
REACTIVE_POWER_PERCENTAGE = [(2, 1552, 1, "无功功率百分比"), ]
POWER_FACTOR = [(2, 1555, 1, "功率因数"), ]
VOLTAGE_L1 = [(3, 507, 1, "L1 电压"), ]
VOLTAGE_L2 = [(3, 508, 1, "L2 电压"), ]
VOLTAGE_L3 = [(3, 509, 1, "L3 电压"), ]
CURRENT_A = [(3, 510, 1, "A 相电流"), ]
CURRENT_B = [(3, 511, 1, "B 相电流"), ]
CURRENT_C = [(3, 512, 1, "C 相电流"), ]
INVERTER_SWITCH = [( ), ]
RATED_ACTIVE_POWER = [(3, 7, 2, "额定有功"), ]
RATED_REACTIVE_POWER = [(3, 9, 2, "额定无功"), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(3, 41, 8, "序列号"), ]
OPERATIONAL_STATUS = [(3, 2500, 1, "运行状态"), ]
ACTIVE_POWER = [(3, 522, 2, "有功功率" ), ]
REACTIVE_POWER = [(3, 524, 2, "无功功率" ),]

[kehua]
ACTIVE_POWER_CONTROL = [( ), ]
ACTIVE_POWER_PERCENTAGE = [(2,6002, 1, "有功功率百分比"),]
REACTIVE_POWER_CONTROL = [(2, 6001, 1, "无功功率"), ]
REACTIVE_POWER_PERCENTAGE = [( ), ]
POWER_FACTOR = [(2, 6000, 1, "功率因数"), ]
VOLTAGE_L1 = [(3, 4510, 1, "L1 电压"), ]
VOLTAGE_L2 = [( ), ]
VOLTAGE_L3 = [( ), ]
CURRENT_A = [(3, 4511, 1, "A 相电流"), ]
CURRENT_B = [( ), ]
CURRENT_C = [( ), ]
INVERTER_SWITCH = [(2, 5000, 1, "逆变器开关"), ]
RATED_ACTIVE_POWER = [( ), ]
RATED_REACTIVE_POWER = [( ), ]
OUTPUT_TYPE = [( ), ]
SERIAL_NUMBER = [(3, 4596, 10, "序列号"), ]
OPERATIONAL_STATUS = [(3, 4501, 1, "运行状态"), ]
ACTIVE_POWER = [(3, 4508, 1, "有功功率" ), ]
REACTIVE_POWER = [(3, 4509, 1, "无功功率" ),]



















