package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v2"
)

// SaveConfig 将配置保存到文件
func SaveConfig(path string, cfg *Config) error {
	// 确保目录存在
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	// 将配置转换为 YAML
	data, err := yaml.Marshal(cfg)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(path, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

// SaveConfigWithPositions 将配置保存到文件，特别处理 position 节点和 item 节点
func SaveConfigWithPositions(path string, cfg *Config) error {
	// 确保目录存在
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	// 创建一个临时结构体，只包含基本配置
	type BasicConfig struct {
		Detector DetectorConfig `yaml:"detector"`
		Input    InputConfig    `yaml:"input"`
		Output   OutputConfig   `yaml:"output"`
	}

	basicConfig := BasicConfig{
		Detector: cfg.Detector,
		Input:    cfg.Input,
		Output:   cfg.Output,
	}

	// 将基本配置转换为 YAML
	basicData, err := yaml.Marshal(basicConfig)
	if err != nil {
		return fmt.Errorf("序列化基本配置失败: %w", err)
	}

	// 创建文件内容
	content := string(basicData) + "\n\n"

	// 添加每个 position 节点
	for _, pos := range cfg.GetPositions() {
		// 创建一个临时结构体，只包含单个 position 节点
		type PositionWrapper struct {
			Position PositionConfig `yaml:"position"`
		}

		wrapper := PositionWrapper{
			Position: pos,
		}

		// 将 position 节点转换为 YAML
		posData, err := yaml.Marshal(wrapper)
		if err != nil {
			return fmt.Errorf("序列化 position 节点失败: %w", err)
		}

		// 添加到文件内容
		content += string(posData) + "\n"
	}

	// 添加每个 item 节点
	for _, item := range cfg.GetItems() {
		// 创建一个临时结构体，只包含单个 item 节点
		type ItemWrapper struct {
			Item ItemConfig `yaml:"item"`
		}

		wrapper := ItemWrapper{
			Item: item,
		}

		// 将 item 节点转换为 YAML
		itemData, err := yaml.Marshal(wrapper)
		if err != nil {
			return fmt.Errorf("序列化 item 节点失败: %w", err)
		}

		// 添加到文件内容
		content += string(itemData) + "\n"
	}

	// 写入文件
	if err := os.WriteFile(path, []byte(content), 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}
