package common

import (
	"errors"
	"fmt"
	"io"
	"sync"
	"time"

	"go.bug.st/serial"
)

// SerialConfig 表示串口配置
type SerialConfig struct {
	Port     string // 端口名称
	BaudRate int    // 波特率
	DataBits int    // 数据位
	StopBits int    // 停止位
	Parity   string // 校验位
	Timeout  int    // 超时时间(毫秒)
}

// SerialPort 表示串口
type SerialPort struct {
	config SerialConfig
	port   serial.Port
	mutex  sync.Mutex
	isOpen bool
}

// NewSerialPort 创建一个新的串口
func NewSerialPort(config SerialConfig) *SerialPort {
	return &SerialPort{
		config: config,
		isOpen: false,
	}
}

// Open 打开串口
func (s *SerialPort) Open() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isOpen {
		return nil
	}

	// 转换校验位
	var parity serial.Parity
	switch s.config.Parity {
	case "N", "n":
		parity = serial.NoParity
	case "E", "e":
		parity = serial.EvenParity
	case "O", "o":
		parity = serial.OddParity
	case "M", "m":
		parity = serial.MarkParity
	case "S", "s":
		parity = serial.SpaceParity
	default:
		return fmt.Errorf("不支持的校验位: %s", s.config.Parity)
	}

	// 转换停止位
	var stopBits serial.StopBits
	switch s.config.StopBits {
	case 1:
		stopBits = serial.OneStopBit
	case 2:
		stopBits = serial.TwoStopBits
	case 15:
		stopBits = serial.OnePointFiveStopBits
	default:
		return fmt.Errorf("不支持的停止位: %d", s.config.StopBits)
	}

	// 创建串口配置
	mode := &serial.Mode{
		BaudRate: s.config.BaudRate,
		DataBits: s.config.DataBits,
		Parity:   parity,
		StopBits: stopBits,
	}

	// 打开串口
	port, err := serial.Open(s.config.Port, mode)
	if err != nil {
		return fmt.Errorf("打开串口失败: %w", err)
	}

	// 设置超时
	if s.config.Timeout > 0 {
		err = port.SetReadTimeout(time.Duration(s.config.Timeout) * time.Millisecond)
		if err != nil {
			port.Close()
			return fmt.Errorf("设置超时失败: %w", err)
		}
	}

	s.port = port
	s.isOpen = true
	return nil
}

// Close 关闭串口
func (s *SerialPort) Close() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isOpen {
		return nil
	}

	err := s.port.Close()
	if err != nil {
		return fmt.Errorf("关闭串口失败: %w", err)
	}

	s.isOpen = false
	return nil
}

// Write 写入数据
func (s *SerialPort) Write(data []byte) (int, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isOpen {
		return 0, errors.New("串口未打开")
	}

	return s.port.Write(data)
}

// Read 读取数据
func (s *SerialPort) Read(buffer []byte) (int, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isOpen {
		return 0, errors.New("串口未打开")
	}

	return s.port.Read(buffer)
}

// ReadWithTimeout 带超时的读取数据
func (s *SerialPort) ReadWithTimeout(buffer []byte, timeout time.Duration) (int, error) {
	s.mutex.Lock()

	if !s.isOpen {
		s.mutex.Unlock()
		return 0, errors.New("串口未打开")
	}

	// 保存原始超时
	originalTimeout := s.config.Timeout

	// 设置新超时
	err := s.port.SetReadTimeout(timeout)
	if err != nil {
		s.mutex.Unlock()
		return 0, fmt.Errorf("设置超时失败: %w", err)
	}

	s.mutex.Unlock()

	// 读取数据
	n, err := s.Read(buffer)

	// 恢复原始超时
	s.mutex.Lock()
	if s.isOpen {
		s.port.SetReadTimeout(time.Duration(originalTimeout) * time.Millisecond)
	}
	s.mutex.Unlock()

	return n, err
}

// ReadAll 读取所有可用数据
func (s *SerialPort) ReadAll() ([]byte, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isOpen {
		return nil, errors.New("串口未打开")
	}

	buffer := make([]byte, 1024)
	var result []byte

	for {
		n, err := s.port.Read(buffer)
		if err != nil {
			if err == io.EOF {
				break
			}
			return result, err
		}
		if n == 0 {
			break
		}
		result = append(result, buffer[:n]...)
	}

	return result, nil
}

// Flush 清空缓冲区
func (s *SerialPort) Flush() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isOpen {
		return errors.New("串口未打开")
	}

	return s.port.ResetInputBuffer()
}

// IsOpen 检查串口是否打开
func (s *SerialPort) IsOpen() bool {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	return s.isOpen
}

// GetConfig 获取串口配置
func (s *SerialPort) GetConfig() SerialConfig {
	return s.config
}

// SetConfig 设置串口配置
func (s *SerialPort) SetConfig(config SerialConfig) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isOpen {
		return errors.New("串口已打开，无法修改配置")
	}

	s.config = config
	return nil
}

// ListPorts 列出所有可用串口
func ListPorts() ([]string, error) {
	ports, err := serial.GetPortsList()
	if err != nil {
		return nil, fmt.Errorf("获取串口列表失败: %w", err)
	}
	return ports, nil
}
