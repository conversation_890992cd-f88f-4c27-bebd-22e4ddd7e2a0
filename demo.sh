#!/bin/bash

echo "🚀 Go Plugin System 演示"
echo "========================"
echo ""
echo "这是一个基于 go-plugin 构建的动态插件系统演示"
echo "插件与宿主进程通过 gRPC 通信，支持动态加载 .so/.dll 文件"
echo ""

# 检查是否已构建
if [ ! -f "build/host/plugin-host" ]; then
    echo "📦 正在构建项目..."
    make build
    echo ""
fi

echo "🔌 可用插件："
echo "1. example-plugin - 演示插件，支持多种文本操作"
echo "2. calculator-plugin - 简单计算器插件"
echo ""

echo "💡 使用说明："
echo "- 输入 'help' 查看所有命令"
echo "- 输入 'list' 查看已加载的插件"
echo "- 使用 'init <plugin-name> [config]' 初始化插件"
echo "- 使用 'execute <plugin-name> <input>' 执行插件"
echo "- 输入 'quit' 退出系统"
echo ""

echo "🎯 示例命令："
echo "  init example-plugin greeting=Hello"
echo "  execute example-plugin hello"
echo "  execute calculator-plugin 10 + 5"
echo "  execute example-plugin reverse:Hello World"
echo ""

echo "🏃 启动插件宿主程序..."
echo "========================"
echo ""

cd build/host && ./plugin-host ../plugins
