package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"plugin.PVDIM/shared"
	"strings"
	"sync"

	"github.com/hashicorp/go-plugin"
)

// PluginManager 管理插件的加载、卸载和执行
type PluginManager struct {
	plugins map[string]*LoadedPlugin
	mutex   sync.RWMutex
}

// LoadedPlugin 表示已加载的插件
type LoadedPlugin struct {
	Name     string
	Version  string
	Client   *plugin.Client
	Instance shared.PluginInterface
	Info     *shared.PluginInfo
}

// NewPluginManager 创建新的插件管理器
func NewPluginManager() *PluginManager {
	return &PluginManager{
		plugins: make(map[string]*LoadedPlugin),
	}
}

// LoadPlugin 从文件路径加载插件
func (pm *PluginManager) LoadPlugin(pluginPath string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 检查文件是否存在
	if _, err := os.Stat(pluginPath); os.IsNotExist(err) {
		return fmt.Errorf("plugin file does not exist: %s", pluginPath)
	}

	// 创建插件客户端
	client := plugin.NewClient(&plugin.ClientConfig{
		HandshakeConfig: shared.Handshake,
		Plugins:         shared.PluginMap,
		Cmd:             exec.Command(pluginPath),
		AllowedProtocols: []plugin.Protocol{
			plugin.ProtocolGRPC,
		},
	})

	// 连接到插件
	rpcClient, err := client.Client()
	if err != nil {
		client.Kill()
		return fmt.Errorf("failed to connect to plugin: %v", err)
	}

	// 获取插件实例
	raw, err := rpcClient.Dispense(shared.PluginName)
	if err != nil {
		client.Kill()
		return fmt.Errorf("failed to dispense plugin: %v", err)
	}

	pluginInstance := raw.(shared.PluginInterface)

	// 获取插件信息
	name := pluginInstance.GetName()
	version := pluginInstance.GetVersion()

	// 检查是否已经加载了同名插件
	if existing, exists := pm.plugins[name]; exists {
		log.Printf("Plugin %s is already loaded (version %s), replacing with version %s", 
			name, existing.Version, version)
		existing.Client.Kill()
	}

	// 创建加载的插件对象
	loadedPlugin := &LoadedPlugin{
		Name:     name,
		Version:  version,
		Client:   client,
		Instance: pluginInstance,
		Info: &shared.PluginInfo{
			Name:        name,
			Version:     version,
			Description: fmt.Sprintf("Plugin loaded from %s", pluginPath),
		},
	}

	pm.plugins[name] = loadedPlugin
	log.Printf("Successfully loaded plugin: %s (version %s)", name, version)

	return nil
}

// UnloadPlugin 卸载指定名称的插件
func (pm *PluginManager) UnloadPlugin(name string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	loadedPlugin, exists := pm.plugins[name]
	if !exists {
		return fmt.Errorf("plugin %s is not loaded", name)
	}

	// 清理插件资源
	if err := loadedPlugin.Instance.Cleanup(); err != nil {
		log.Printf("Warning: plugin %s cleanup failed: %v", name, err)
	}

	// 终止插件进程
	loadedPlugin.Client.Kill()

	// 从映射中删除
	delete(pm.plugins, name)

	log.Printf("Successfully unloaded plugin: %s", name)
	return nil
}

// GetPlugin 获取指定名称的插件实例
func (pm *PluginManager) GetPlugin(name string) (shared.PluginInterface, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	loadedPlugin, exists := pm.plugins[name]
	if !exists {
		return nil, fmt.Errorf("plugin %s is not loaded", name)
	}

	return loadedPlugin.Instance, nil
}

// ListPlugins 列出所有已加载的插件
func (pm *PluginManager) ListPlugins() []*shared.PluginInfo {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	var plugins []*shared.PluginInfo
	for _, loadedPlugin := range pm.plugins {
		plugins = append(plugins, loadedPlugin.Info)
	}

	return plugins
}

// ExecutePlugin 执行指定插件的功能
func (pm *PluginManager) ExecutePlugin(ctx context.Context, name string, input string) (string, error) {
	pluginInstance, err := pm.GetPlugin(name)
	if err != nil {
		return "", err
	}

	return pluginInstance.Execute(ctx, input)
}

// LoadPluginsFromDirectory 从目录加载所有插件
func (pm *PluginManager) LoadPluginsFromDirectory(dir string) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return fmt.Errorf("plugin directory does not exist: %s", dir)
	}

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查文件扩展名（支持 .so 和 .dll）
		ext := strings.ToLower(filepath.Ext(path))
		if ext != ".so" && ext != ".dll" && ext != "" {
			return nil
		}

		// 确保文件是可执行的
		if info.Mode()&0111 == 0 {
			return nil
		}

		log.Printf("Loading plugin from: %s", path)
		if err := pm.LoadPlugin(path); err != nil {
			log.Printf("Failed to load plugin %s: %v", path, err)
			// 继续加载其他插件，不返回错误
		}

		return nil
	})

	return err
}

// Shutdown 关闭插件管理器，清理所有插件
func (pm *PluginManager) Shutdown() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	log.Println("Shutting down plugin manager...")

	for name, loadedPlugin := range pm.plugins {
		log.Printf("Cleaning up plugin: %s", name)
		
		// 清理插件资源
		if err := loadedPlugin.Instance.Cleanup(); err != nil {
			log.Printf("Warning: plugin %s cleanup failed: %v", name, err)
		}

		// 终止插件进程
		loadedPlugin.Client.Kill()
	}

	// 清空插件映射
	pm.plugins = make(map[string]*LoadedPlugin)

	log.Println("Plugin manager shutdown complete")
}
