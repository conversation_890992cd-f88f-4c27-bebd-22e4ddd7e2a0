package main

import (
	"bufio"
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"
)

func main() {
	log.Println("Starting Plugin Host Application...")

	// 创建插件管理器
	pluginManager := NewPluginManager()

	// 设置优雅关闭
	setupGracefulShutdown(pluginManager)

	// 加载插件目录中的所有插件
	pluginDir := "./plugins"
	if len(os.Args) > 1 {
		pluginDir = os.Args[1]
	}

	log.Printf("Loading plugins from directory: %s", pluginDir)
	if err := pluginManager.LoadPluginsFromDirectory(pluginDir); err != nil {
		log.Printf("Warning: Failed to load plugins from directory: %v", err)
	}

	// 启动交互式命令行界面
	startInteractiveCLI(pluginManager)
}

func setupGracefulShutdown(pluginManager *PluginManager) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("Received shutdown signal...")
		pluginManager.Shutdown()
		os.Exit(0)
	}()
}

func startInteractiveCLI(pluginManager *PluginManager) {
	scanner := bufio.NewScanner(os.Stdin)

	printHelp()

	for {
		fmt.Print("> ")
		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		if input == "" {
			continue
		}

		parts := strings.Fields(input)
		if len(parts) == 0 {
			continue
		}

		command := strings.ToLower(parts[0])

		switch command {
		case "help", "h":
			printHelp()

		case "list", "ls":
			listPlugins(pluginManager)

		case "load":
			if len(parts) < 2 {
				fmt.Println("Usage: load <plugin_path>")
				continue
			}
			loadPlugin(pluginManager, parts[1])

		case "unload":
			if len(parts) < 2 {
				fmt.Println("Usage: unload <plugin_name>")
				continue
			}
			unloadPlugin(pluginManager, parts[1])

		case "execute", "exec":
			if len(parts) < 2 {
				fmt.Println("Usage: execute <plugin_name> [input]")
				continue
			}
			pluginName := parts[1]
			input := ""
			if len(parts) > 2 {
				input = strings.Join(parts[2:], " ")
			}
			executePlugin(pluginManager, pluginName, input)

		case "init":
			if len(parts) < 2 {
				fmt.Println("Usage: init <plugin_name> [key=value ...]")
				continue
			}
			initializePlugin(pluginManager, parts[1:])

		case "quit", "exit", "q":
			fmt.Println("Shutting down...")
			pluginManager.Shutdown()
			return

		default:
			fmt.Printf("Unknown command: %s. Type 'help' for available commands.\n", command)
		}
	}
}

func printHelp() {
	fmt.Println("\n=== Plugin Host Commands ===")
	fmt.Println("help, h          - Show this help message")
	fmt.Println("list, ls         - List all loaded plugins")
	fmt.Println("load <path>      - Load a plugin from file path")
	fmt.Println("unload <name>    - Unload a plugin by name")
	fmt.Println("execute <name> [input] - Execute a plugin with optional input")
	fmt.Println("init <name> [key=value ...] - Initialize a plugin with config")
	fmt.Println("quit, exit, q    - Exit the application")
	fmt.Println("=============================")
}

func listPlugins(pluginManager *PluginManager) {
	plugins := pluginManager.ListPlugins()
	if len(plugins) == 0 {
		fmt.Println("No plugins loaded.")
		return
	}

	fmt.Printf("\nLoaded Plugins (%d):\n", len(plugins))
	fmt.Println("Name\t\tVersion\t\tDescription")
	fmt.Println("----\t\t-------\t\t-----------")
	for _, plugin := range plugins {
		fmt.Printf("%s\t\t%s\t\t%s\n", plugin.Name, plugin.Version, plugin.Description)
	}
	fmt.Println()
}

func loadPlugin(pluginManager *PluginManager, pluginPath string) {
	fmt.Printf("Loading plugin from: %s\n", pluginPath)
	if err := pluginManager.LoadPlugin(pluginPath); err != nil {
		fmt.Printf("Failed to load plugin: %v\n", err)
	} else {
		fmt.Println("Plugin loaded successfully!")
	}
}

func unloadPlugin(pluginManager *PluginManager, pluginName string) {
	fmt.Printf("Unloading plugin: %s\n", pluginName)
	if err := pluginManager.UnloadPlugin(pluginName); err != nil {
		fmt.Printf("Failed to unload plugin: %v\n", err)
	} else {
		fmt.Println("Plugin unloaded successfully!")
	}
}

func executePlugin(pluginManager *PluginManager, pluginName, input string) {
	fmt.Printf("Executing plugin '%s' with input: '%s'\n", pluginName, input)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	output, err := pluginManager.ExecutePlugin(ctx, pluginName, input)
	if err != nil {
		fmt.Printf("Plugin execution failed: %v\n", err)
		return
	}

	fmt.Printf("Plugin output: %s\n", output)
}

func initializePlugin(pluginManager *PluginManager, args []string) {
	if len(args) < 1 {
		fmt.Println("Usage: init <plugin_name> [key=value ...]")
		return
	}

	pluginName := args[0]
	config := make(map[string]string)

	// 解析配置参数
	for _, arg := range args[1:] {
		parts := strings.SplitN(arg, "=", 2)
		if len(parts) == 2 {
			config[parts[0]] = parts[1]
		} else {
			fmt.Printf("Invalid config format: %s (expected key=value)\n", arg)
			return
		}
	}

	fmt.Printf("Initializing plugin '%s' with config: %v\n", pluginName, config)

	plugin, err := pluginManager.GetPlugin(pluginName)
	if err != nil {
		fmt.Printf("Failed to get plugin: %v\n", err)
		return
	}

	if err := plugin.Initialize(config); err != nil {
		fmt.Printf("Plugin initialization failed: %v\n", err)
		return
	}

	fmt.Println("Plugin initialized successfully!")
}
