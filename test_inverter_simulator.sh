#!/bin/bash

# 逆变器模拟器测试脚本

echo "🔌 逆变器模拟器测试"
echo "===================="
echo ""

# 检查构建文件
echo "1. 检查构建文件..."
if [ ! -f "build/host/plugin-host" ]; then
    echo "❌ 宿主程序未找到，正在构建..."
    go build -o build/host/plugin-host ./host
fi

if [ ! -f "build/plugins/inverter-simulator" ]; then
    echo "❌ 逆变器模拟器插件未找到，正在构建..."
    go build -o build/plugins/inverter-simulator ./plugins/inverter-simulator
fi

echo "✅ 构建文件检查完成"

# 检查配置文件
echo "2. 检查配置文件..."
if [ ! -f "internal/config/inverter/config.ini" ]; then
    echo "❌ 配置文件不存在"
    exit 1
fi

if [ ! -f "internal/config/inverter/0.BiaoZhun.csv" ]; then
    echo "❌ 数据文件不存在"
    exit 1
fi

echo "✅ 配置文件检查完成"

# 设置权限
echo "3. 设置文件权限..."
chmod +x build/host/plugin-host
chmod +x build/plugins/inverter-simulator

echo "✅ 权限设置完成"

# 测试插件系统
echo "4. 测试逆变器模拟器..."
echo "启动插件宿主程序..."

cd build/host

# 创建测试命令文件
cat > test_inverter_commands.txt << 'EOF'
list
init inverter-simulator config_dir=../../internal/config/inverter
execute inverter-simulator "configs"
execute inverter-simulator "status"
execute inverter-simulator "create 1 biaozhun 1"
execute inverter-simulator "list"
execute inverter-simulator "help"
quit
EOF

echo "执行测试命令..."
timeout 60s ./plugin-host ../plugins < test_inverter_commands.txt

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📋 功能说明："
echo "- 逆变器模拟器插件已成功构建"
echo "- 支持多种逆变器品牌配置"
echo "- 提供 Modbus RTU 协议模拟"
echo "- 支持寄存器读写操作"
echo ""
echo "🎯 使用方法："
echo "1. 启动宿主程序: cd build/host && ./plugin-host ../plugins"
echo "2. 初始化插件: init inverter-simulator config_dir=../../internal/config/inverter"
echo "3. 查看配置: execute inverter-simulator \"configs\""
echo "4. 创建模拟器: execute inverter-simulator \"create 1 biaozhun 1\""
echo "5. 启动模拟器: execute inverter-simulator \"start 1 COM1 9600\""
echo "6. 读取寄存器: execute inverter-simulator \"read 1 3 61697\""
echo ""
echo "📚 更多信息请查看: internal/device/inverter/README.md"
