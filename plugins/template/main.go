package main

import (
	"context"
	"fmt"
	"log"
	"plugin.PVDIM/shared"

	"github.com/hashicorp/go-plugin"
)

// TemplatePlugin 插件模板
type TemplatePlugin struct {
	name        string
	version     string
	initialized bool
	config      map[string]string
}

// NewTemplatePlugin 创建新的模板插件实例
func NewTemplatePlugin() *TemplatePlugin {
	return &TemplatePlugin{
		name:    "template-plugin", // 修改为你的插件名称
		version: "1.0.0",          // 修改为你的插件版本
		config:  make(map[string]string),
	}
}

// GetName 返回插件名称
func (p *TemplatePlugin) GetName() string {
	return p.name
}

// GetVersion 返回插件版本
func (p *TemplatePlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *TemplatePlugin) Initialize(config map[string]string) error {
	log.Printf("Initializing %s plugin with config: %v", p.name, config)
	
	p.config = config
	p.initialized = true
	
	// 在这里添加你的初始化逻辑
	// 例如：连接数据库、读取配置文件等
	
	return nil
}

// Execute 执行插件主要功能
func (p *TemplatePlugin) Execute(ctx context.Context, input string) (string, error) {
	if !p.initialized {
		return "", fmt.Errorf("plugin %s is not initialized", p.name)
	}

	log.Printf("Executing %s plugin with input: %s", p.name, input)

	// 检查上下文是否被取消
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	// 在这里添加你的业务逻辑
	// 根据输入参数执行相应的操作
	
	// 示例：简单的回显功能
	result := fmt.Sprintf("Template plugin processed: %s", input)
	
	return result, nil
}

// Cleanup 清理插件资源
func (p *TemplatePlugin) Cleanup() error {
	log.Printf("Cleaning up %s plugin", p.name)
	
	// 在这里添加资源清理逻辑
	// 例如：关闭数据库连接、清理临时文件等
	
	p.initialized = false
	p.config = make(map[string]string)
	
	return nil
}

func main() {
	// 创建插件实例
	pluginImpl := NewTemplatePlugin()

	// 创建插件映射
	pluginMap := map[string]plugin.Plugin{
		shared.PluginName: &shared.PluginGRPC{Impl: pluginImpl},
	}

	log.Printf("Starting %s plugin (v%s)", pluginImpl.GetName(), pluginImpl.GetVersion())

	// 启动插件服务
	plugin.Serve(&plugin.ServeConfig{
		HandshakeConfig: shared.Handshake,
		Plugins:         pluginMap,
		GRPCServer:      plugin.DefaultGRPCServer,
	})
}
