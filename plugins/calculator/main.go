package main

import (
	"context"
	"fmt"
	"log"
	"plugin.PVDIM/shared"
	"strconv"
	"strings"

	"github.com/hashicorp/go-plugin"
)

// CalculatorPlugin 计算器插件实现
type CalculatorPlugin struct {
	name        string
	version     string
	initialized bool
	config      map[string]string
}

// NewCalculatorPlugin 创建新的计算器插件实例
func NewCalculatorPlugin() *CalculatorPlugin {
	return &CalculatorPlugin{
		name:    "calculator-plugin",
		version: "1.0.0",
		config:  make(map[string]string),
	}
}

// GetName 返回插件名称
func (p *CalculatorPlugin) GetName() string {
	return p.name
}

// GetVersion 返回插件版本
func (p *CalculatorPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *CalculatorPlugin) Initialize(config map[string]string) error {
	log.Printf("Initializing %s plugin with config: %v", p.name, config)
	
	p.config = config
	p.initialized = true
	
	return nil
}

// Execute 执行插件主要功能
func (p *CalculatorPlugin) Execute(ctx context.Context, input string) (string, error) {
	if !p.initialized {
		return "", fmt.Errorf("plugin %s is not initialized", p.name)
	}

	log.Printf("Executing %s plugin with input: %s", p.name, input)

	// 检查上下文是否被取消
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	// 解析计算表达式
	result, err := p.calculate(input)
	if err != nil {
		return "", fmt.Errorf("calculation error: %v", err)
	}

	return fmt.Sprintf("Result: %g", result), nil
}

// Cleanup 清理插件资源
func (p *CalculatorPlugin) Cleanup() error {
	log.Printf("Cleaning up %s plugin", p.name)
	
	p.initialized = false
	p.config = make(map[string]string)
	
	return nil
}

// calculate 执行简单的数学计算
func (p *CalculatorPlugin) calculate(expression string) (float64, error) {
	expression = strings.TrimSpace(expression)
	
	// 支持的操作格式: "数字1 操作符 数字2"
	parts := strings.Fields(expression)
	if len(parts) != 3 {
		return 0, fmt.Errorf("invalid expression format. Use: <number1> <operator> <number2>")
	}

	// 解析第一个数字
	num1, err := strconv.ParseFloat(parts[0], 64)
	if err != nil {
		return 0, fmt.Errorf("invalid first number: %s", parts[0])
	}

	// 获取操作符
	operator := parts[1]

	// 解析第二个数字
	num2, err := strconv.ParseFloat(parts[2], 64)
	if err != nil {
		return 0, fmt.Errorf("invalid second number: %s", parts[2])
	}

	// 执行计算
	switch operator {
	case "+":
		return num1 + num2, nil
	case "-":
		return num1 - num2, nil
	case "*", "×":
		return num1 * num2, nil
	case "/", "÷":
		if num2 == 0 {
			return 0, fmt.Errorf("division by zero")
		}
		return num1 / num2, nil
	case "^", "**":
		// 简单的幂运算实现
		result := 1.0
		exp := int(num2)
		if exp < 0 {
			return 0, fmt.Errorf("negative exponents not supported")
		}
		for i := 0; i < exp; i++ {
			result *= num1
		}
		return result, nil
	default:
		return 0, fmt.Errorf("unsupported operator: %s. Supported: +, -, *, /, ^", operator)
	}
}

func main() {
	// 创建插件实例
	pluginImpl := NewCalculatorPlugin()

	// 创建插件映射
	pluginMap := map[string]plugin.Plugin{
		shared.PluginName: &shared.PluginGRPC{Impl: pluginImpl},
	}

	log.Printf("Starting %s plugin (v%s)", pluginImpl.GetName(), pluginImpl.GetVersion())

	// 启动插件服务
	plugin.Serve(&plugin.ServeConfig{
		HandshakeConfig: shared.Handshake,
		Plugins:         pluginMap,
		GRPCServer:      plugin.DefaultGRPCServer,
	})
}
