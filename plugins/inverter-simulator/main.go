package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"path/filepath"

	"plugin.PVDIM/internal/device/inverter"
	"plugin.PVDIM/shared"
	"plugin.PVDIM/shared/device"

	"github.com/hashicorp/go-plugin"
	"google.golang.org/grpc"
)

// InverterGRPCPlugin 逆变器 gRPC 插件
type InverterGRPCPlugin struct {
	name          string
	version       string
	initialized   bool
	configDir     string
	grpcServer    *grpc.Server
	grpcListener  net.Listener
	inverterSvc   *inverter.InverterService
	deviceManager *inverter.DeviceManager
}

// NewInverterGRPCPlugin 创建逆变器 gRPC 插件实例
func NewInverterGRPCPlugin() *InverterGRPCPlugin {
	return &InverterGRPCPlugin{
		name:      "inverter-simulator",
		version:   "1.0.0",
		configDir: "./internal/config/inverter", // 默认配置目录
	}
}

// GetName 返回插件名称
func (p *InverterGRPCPlugin) GetName() string {
	return p.name
}

// GetVersion 返回插件版本
func (p *InverterGRPCPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *InverterGRPCPlugin) Initialize(config map[string]string) error {
	log.Printf("Initializing %s plugin with config: %v", p.name, config)

	// 从配置中获取配置目录
	if configDir, exists := config["config_dir"]; exists {
		p.configDir = configDir
	}

	// 确保配置目录路径是绝对路径
	if !filepath.IsAbs(p.configDir) {
		absPath, err := filepath.Abs(p.configDir)
		if err != nil {
			return fmt.Errorf("获取配置目录绝对路径失败: %v", err)
		}
		p.configDir = absPath
	}

	// 启动 gRPC 服务器
	if err := p.startGRPCServer(); err != nil {
		return fmt.Errorf("启动 gRPC 服务器失败: %v", err)
	}

	p.initialized = true
	log.Printf("逆变器模拟器插件初始化成功，配置目录: %s", p.configDir)
	log.Printf("gRPC 服务器已启动，监听地址: %s", p.grpcListener.Addr().String())

	return nil
}

// startGRPCServer 启动 gRPC 服务器
func (p *InverterGRPCPlugin) startGRPCServer() error {
	// 监听端口 (使用动态端口)
	listener, err := net.Listen("tcp", ":0")
	if err != nil {
		return fmt.Errorf("监听端口失败: %v", err)
	}
	p.grpcListener = listener

	// 创建 gRPC 服务器
	p.grpcServer = grpc.NewServer()

	// 延迟创建逆变器服务，避免在插件加载时就失败
	inverterSvc, err := inverter.NewInverterService(p.configDir)
	if err != nil {
		return fmt.Errorf("创建逆变器服务失败: %v", err)
	}
	p.inverterSvc = inverterSvc

	// 注册逆变器服务
	device.RegisterInverterServiceServer(p.grpcServer, p.inverterSvc)

	// 在后台启动服务器
	go func() {
		log.Printf("gRPC 服务器启动在端口: %s", listener.Addr().String())
		if err := p.grpcServer.Serve(listener); err != nil {
			log.Printf("gRPC 服务器错误: %v", err)
		}
	}()

	return nil
}

// Execute 执行插件主要功能
func (p *InverterGRPCPlugin) Execute(ctx context.Context, input string) (string, error) {
	if !p.initialized {
		return "", fmt.Errorf("plugin %s is not initialized", p.name)
	}

	log.Printf("Executing %s plugin with input: %s", p.name, input)

	// 检查上下文是否被取消
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	// 处理基本命令
	switch input {
	case "status":
		return p.getStatus(), nil
	case "grpc-address":
		if p.grpcListener != nil {
			return p.grpcListener.Addr().String(), nil
		}
		return "gRPC 服务器未启动", nil
	case "help":
		return p.getHelp(), nil
	default:
		return fmt.Sprintf("逆变器模拟器 gRPC 服务运行中\n服务地址: %s\n命令: %s",
			p.grpcListener.Addr().String(), input), nil
	}
}

// getStatus 获取状态信息
func (p *InverterGRPCPlugin) getStatus() string {
	status := fmt.Sprintf(`逆变器模拟器状态:
- 插件名称: %s
- 版本: %s
- 初始化状态: %t
- 配置目录: %s
- gRPC 服务地址: %s
- 设备管理器状态: 运行中`,
		p.name, p.version, p.initialized, p.configDir,
		p.grpcListener.Addr().String())

	if p.deviceManager != nil {
		deviceStatus := p.deviceManager.GetStatus()
		status += fmt.Sprintf("\n- 活跃设备数量: %d", len(deviceStatus))
	}

	return status
}

// getHelp 获取帮助信息
func (p *InverterGRPCPlugin) getHelp() string {
	return fmt.Sprintf(`逆变器模拟器 gRPC 插件帮助:

插件信息:
- 名称: %s
- 版本: %s
- gRPC 服务地址: %s

可用命令:
- status: 查看插件和服务状态
- grpc-address: 获取 gRPC 服务地址
- help: 显示此帮助信息

gRPC 接口:
- Initialize: 初始化逆变器设备
- Cleanup: 清理逆变器设备
- GetData: 获取设备数据
- SetData: 设置设备数据
- ModifyCommParam: 修改通信参数
- ModifyProtocol: 修改协议

使用方法:
1. 通过 gRPC 客户端连接到服务地址
2. 调用 Initialize 接口创建模拟设备
3. 使用其他接口操作设备
4. 设备将自动响应 Modbus 通信请求

示例 gRPC 调用:
- 初始化设备: Initialize(position=1, protocol="biaozhun", comm_param="COM1,9600,1")
- 获取数据: GetData(position=1, param_type=61697)
- 设置数据: SetData(position=1, param_type=61697, value="100")`,
		p.name, p.version, p.grpcListener.Addr().String())
}

// Cleanup 清理插件资源
func (p *InverterGRPCPlugin) Cleanup() error {
	log.Printf("Cleaning up %s plugin", p.name)

	// 停止 gRPC 服务器
	if p.grpcServer != nil {
		log.Printf("停止 gRPC 服务器...")
		p.grpcServer.GracefulStop()
		p.grpcServer = nil
	}

	// 关闭监听器
	if p.grpcListener != nil {
		p.grpcListener.Close()
		p.grpcListener = nil
	}

	// 清理设备管理器
	if p.deviceManager != nil {
		if err := p.deviceManager.StopAll(); err != nil {
			log.Printf("清理设备管理器失败: %v", err)
		}
		p.deviceManager = nil
	}

	p.initialized = false
	log.Printf("逆变器模拟器插件清理完成")
	return nil
}

func main() {
	// 创建插件实例
	pluginImpl := NewInverterGRPCPlugin()

	// 创建插件映射
	pluginMap := map[string]plugin.Plugin{
		shared.PluginName: &shared.PluginGRPC{Impl: pluginImpl},
	}

	log.Printf("Starting %s plugin (v%s)", pluginImpl.GetName(), pluginImpl.GetVersion())

	// 启动插件服务
	plugin.Serve(&plugin.ServeConfig{
		HandshakeConfig: shared.Handshake,
		Plugins:         pluginMap,
		GRPCServer:      plugin.DefaultGRPCServer,
	})
}
