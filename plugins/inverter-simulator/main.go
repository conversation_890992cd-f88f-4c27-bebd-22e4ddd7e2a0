package main

import (
	"context"
	"fmt"
	"log"

	"plugin.PVDIM/shared"

	"github.com/hashicorp/go-plugin"
)

// SimpleInverterPlugin 简化的逆变器插件
type SimpleInverterPlugin struct {
	name        string
	version     string
	initialized bool
}

// NewSimpleInverterPlugin 创建简化插件实例
func NewSimpleInverterPlugin() *SimpleInverterPlugin {
	return &SimpleInverterPlugin{
		name:    "inverter-simulator",
		version: "1.0.0",
	}
}

// GetName 返回插件名称
func (p *SimpleInverterPlugin) GetName() string {
	return p.name
}

// GetVersion 返回插件版本
func (p *SimpleInverterPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *SimpleInverterPlugin) Initialize(config map[string]string) error {
	log.Printf("Initializing %s plugin with config: %v", p.name, config)
	p.initialized = true
	log.Printf("逆变器模拟器插件初始化成功")
	return nil
}

// Execute 执行插件主要功能
func (p *SimpleInverterPlugin) Execute(ctx context.Context, input string) (string, error) {
	if !p.initialized {
		return "", fmt.Errorf("plugin %s is not initialized", p.name)
	}

	log.Printf("Executing %s plugin with input: %s", p.name, input)

	// 检查上下文是否被取消
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	// 简单的命令处理
	switch input {
	case "status":
		return "逆变器模拟器状态: 运行中", nil
	case "configs":
		return "可用配置:\n1. biaozhun\n2. huawei1\n3. jinlang", nil
	case "help":
		return `逆变器模拟器命令帮助:
- status: 查看状态
- configs: 查看可用配置
- help: 显示帮助`, nil
	default:
		return fmt.Sprintf("逆变器模拟器处理命令: %s", input), nil
	}
}

// Cleanup 清理插件资源
func (p *SimpleInverterPlugin) Cleanup() error {
	log.Printf("Cleaning up %s plugin", p.name)
	p.initialized = false
	return nil
}

func main() {
	// 创建插件实例
	pluginImpl := NewSimpleInverterPlugin()

	// 创建插件映射
	pluginMap := map[string]plugin.Plugin{
		shared.PluginName: &shared.PluginGRPC{Impl: pluginImpl},
	}

	log.Printf("Starting %s plugin (v%s)", pluginImpl.GetName(), pluginImpl.GetVersion())

	// 启动插件服务
	plugin.Serve(&plugin.ServeConfig{
		HandshakeConfig: shared.Handshake,
		Plugins:         pluginMap,
		GRPCServer:      plugin.DefaultGRPCServer,
	})
}
