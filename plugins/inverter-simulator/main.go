package main

import (
	"context"
	"fmt"
	"log"
	"path/filepath"

	"plugin.PVDIM/internal/device/inverter"
	"plugin.PVDIM/shared"

	"github.com/hashicorp/go-plugin"
)

// InverterSimulatorPlugin 逆变器模拟器插件
type InverterSimulatorPlugin struct {
	name          string
	version       string
	initialized   bool
	deviceManager *inverter.DeviceManager
	configDir     string
}

// NewInverterSimulatorPlugin 创建逆变器模拟器插件实例
func NewInverterSimulatorPlugin() *InverterSimulatorPlugin {
	return &InverterSimulatorPlugin{
		name:      "inverter-simulator",
		version:   "1.0.0",
		configDir: "./internal/config/inverter", // 默认配置目录
	}
}

// GetName 返回插件名称
func (p *InverterSimulatorPlugin) GetName() string {
	return p.name
}

// GetVersion 返回插件版本
func (p *InverterSimulatorPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *InverterSimulatorPlugin) Initialize(config map[string]string) error {
	log.Printf("Initializing %s plugin with config: %v", p.name, config)

	// 从配置中获取配置目录
	if configDir, exists := config["config_dir"]; exists {
		p.configDir = configDir
	}

	// 确保配置目录路径是绝对路径
	if !filepath.IsAbs(p.configDir) {
		absPath, err := filepath.Abs(p.configDir)
		if err != nil {
			return fmt.Errorf("获取配置目录绝对路径失败: %v", err)
		}
		p.configDir = absPath
	}

	// 创建设备管理器
	deviceManager, err := inverter.NewDeviceManager(p.configDir)
	if err != nil {
		return fmt.Errorf("创建设备管理器失败: %v", err)
	}

	p.deviceManager = deviceManager
	p.initialized = true

	log.Printf("逆变器模拟器插件初始化成功，配置目录: %s", p.configDir)
	return nil
}

// Execute 执行插件主要功能
func (p *InverterSimulatorPlugin) Execute(ctx context.Context, input string) (string, error) {
	if !p.initialized {
		return "", fmt.Errorf("plugin %s is not initialized", p.name)
	}

	log.Printf("Executing %s plugin with input: %s", p.name, input)

	// 检查上下文是否被取消
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	// 处理命令
	if p.deviceManager != nil {
		// 简单的命令处理逻辑
		return fmt.Sprintf("逆变器模拟器处理命令: %s", input), nil
	}

	return "", fmt.Errorf("服务未初始化")
}

// Cleanup 清理插件资源
func (p *InverterSimulatorPlugin) Cleanup() error {
	log.Printf("Cleaning up %s plugin", p.name)

	if p.deviceManager != nil {
		// 清理设备管理器资源
		if err := p.deviceManager.StopAll(); err != nil {
			log.Printf("清理设备管理器失败: %v", err)
		}
		p.deviceManager = nil
	}

	p.initialized = false
	return nil
}

func main() {
	// 创建插件实例
	pluginImpl := NewInverterSimulatorPlugin()

	// 创建插件映射
	pluginMap := map[string]plugin.Plugin{
		shared.PluginName: &shared.PluginGRPC{Impl: pluginImpl},
	}

	log.Printf("Starting %s plugin (v%s)", pluginImpl.GetName(), pluginImpl.GetVersion())

	// 启动插件服务
	plugin.Serve(&plugin.ServeConfig{
		HandshakeConfig: shared.Handshake,
		Plugins:         pluginMap,
		GRPCServer:      plugin.DefaultGRPCServer,
	})
}
