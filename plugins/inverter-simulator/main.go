package main

import (
	"context"
	"fmt"
	"log"

	"plugin.PVDIM/shared"

	"github.com/hashicorp/go-plugin"
)

// MinimalInverterPlugin 最简化的逆变器插件
type MinimalInverterPlugin struct {
	name        string
	version     string
	initialized bool
}

// NewMinimalInverterPlugin 创建最简化插件实例
func NewMinimalInverterPlugin() *MinimalInverterPlugin {
	return &MinimalInverterPlugin{
		name:    "inverter-simulator",
		version: "1.0.0",
	}
}

// GetName 返回插件名称
func (p *MinimalInverterPlugin) GetName() string {
	return p.name
}

// GetVersion 返回插件版本
func (p *MinimalInverterPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *MinimalInverterPlugin) Initialize(config map[string]string) error {
	log.Printf("Initializing %s plugin with config: %v", p.name, config)
	p.initialized = true
	log.Printf("逆变器模拟器插件初始化成功")
	return nil
}

// Execute 执行插件主要功能
func (p *MinimalInverterPlugin) Execute(ctx context.Context, input string) (string, error) {
	if !p.initialized {
		return "", fmt.Errorf("plugin %s is not initialized", p.name)
	}

	log.Printf("Executing %s plugin with input: %s", p.name, input)

	// 检查上下文是否被取消
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	// 简单的命令处理
	switch input {
	case "status":
		return "逆变器模拟器状态: 运行中 (最简化版本)", nil
	case "grpc-address":
		return "gRPC 服务器未启动 (最简化版本)", nil
	case "help":
		return `逆变器模拟器最简化版本帮助:
- status: 查看状态
- grpc-address: 查看 gRPC 地址
- help: 显示帮助`, nil
	default:
		return fmt.Sprintf("逆变器模拟器处理命令: %s (最简化版本)", input), nil
	}
}

// Cleanup 清理插件资源
func (p *MinimalInverterPlugin) Cleanup() error {
	log.Printf("Cleaning up %s plugin", p.name)
	p.initialized = false
	return nil
}

func main() {
	// 创建插件实例
	pluginImpl := NewMinimalInverterPlugin()

	// 创建插件映射
	pluginMap := map[string]plugin.Plugin{
		shared.PluginName: &shared.PluginGRPC{Impl: pluginImpl},
	}

	log.Printf("Starting %s plugin (v%s)", pluginImpl.GetName(), pluginImpl.GetVersion())

	// 启动插件服务
	plugin.Serve(&plugin.ServeConfig{
		HandshakeConfig: shared.Handshake,
		Plugins:         pluginMap,
		GRPCServer:      plugin.DefaultGRPCServer,
	})
}
