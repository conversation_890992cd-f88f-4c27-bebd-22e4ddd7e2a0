package pvdim

import (
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"plugin.PVDIM/internal/common"
	"plugin.PVDIM/internal/config"
	"plugin.PVDIM/internal/logger"
	"plugin.PVDIM/internal/protocol/dlt698"
	"plugin.PVDIM/internal/utils"
)

// DLT698Detector 表示DL/T 698.45协议检测器
type DLT698Detector struct {
	config         *config.Config
	resultCallback ResultCallback
	stopChan       chan struct{}
	running        bool
	mutex          sync.Mutex
	clients        map[int]*dlt698.Client          // 表位ID -> 客户端
	TTLPorts       map[int]*common.SerialPort      // 表位ID -> TTL串口（接收发送 DL/T 698.45 数据）
	Rs485Port      map[int]*common.SerialPort      // 表位ID -> RS485串口（接收发送 Modbus 数据）
	resultsMutex   sync.Mutex                      // 结果互斥锁
	results        map[int]map[int]DetectionResult // 表位ID -> 检测项ID -> 检测结果
}

// NewDLT698Detector 创建一个新的DL/T 698.45协议检测器
func NewDLT698Detector(cfg *config.Config) (*DLT698Detector, error) {
	return &DLT698Detector{
		config:    cfg,
		stopChan:  make(chan struct{}),
		clients:   make(map[int]*dlt698.Client),
		TTLPorts:  make(map[int]*common.SerialPort),
		Rs485Port: make(map[int]*common.SerialPort),
		results:   make(map[int]map[int]DetectionResult),
	}, nil
}

// SetResultCallback 设置结果回调函数
func (d *DLT698Detector) SetResultCallback(callback ResultCallback) {
	d.resultCallback = callback
}

// RunAsync 异步执行检测过程
func (d *DLT698Detector) RunAsync() error {
	d.mutex.Lock()
	if d.running {
		d.mutex.Unlock()
		return fmt.Errorf("检测器已在运行中")
	}
	d.running = true
	d.mutex.Unlock()

	// 重置停止通道
	d.stopChan = make(chan struct{})

	// 在后台运行检测
	go func() {
		defer func() {
			d.mutex.Lock()
			d.running = false
			d.mutex.Unlock()
		}()

		// 获取启用的表位
		positions := d.config.GetPositions()
		enabledPositions := []config.PositionConfig{}
		for _, pos := range positions {
			if pos.Enable {
				enabledPositions = append(enabledPositions, pos)
			}
		}

		// 获取启用的检测项
		items := d.config.GetItems()
		enabledItems := []config.ItemConfig{}
		for _, item := range items {
			if item.Enable {
				enabledItems = append(enabledItems, item)
			}
		}

		// 如果没有启用的表位，返回错误
		if len(enabledPositions) == 0 {
			if d.resultCallback != nil {
				d.resultCallback([]byte(`{"error": "没有启用的表位"}`))
			}
			return
		}

		// 如果没有启用的检测项，返回错误
		if len(enabledItems) == 0 {
			if d.resultCallback != nil {
				d.resultCallback([]byte(`{"error": "没有启用的检测项"}`))
			}
			return
		}

		// 通知前端开始检测
		if d.resultCallback != nil {
			startMsg := map[string]interface{}{
				"status":    "started",
				"positions": len(enabledPositions),
				"items":     len(enabledItems),
				"timestamp": time.Now().Unix(),
			}
			startData, _ := json.Marshal(startMsg)
			d.resultCallback(startData)
		}

		// 初始化所有表位的客户端
		err := d.initClients(enabledPositions)
		if err != nil {
			if d.resultCallback != nil {
				errMsg := fmt.Sprintf(`{"error": "初始化客户端失败: %v", "status": "error"}`, err)
				d.resultCallback([]byte(errMsg))
			}
			return
		}

		// 创建等待组，用于等待所有表位检测完成
		var wg sync.WaitGroup

		// 为每个表位启动一个协程进行检测
		for _, pos := range enabledPositions {
			// 检查是否收到停止信号
			select {
			case <-d.stopChan:
				// 收到停止信号，等待所有协程完成
				wg.Wait()
				if d.resultCallback != nil {
					d.resultCallback([]byte(`{"status": "stopped"}`))
				}
				return
			default:
				// 继续检测
			}

			// 为每个表位启动一个协程
			wg.Add(1)
			go func(position config.PositionConfig) {
				defer wg.Done()
				d.runDetectionForPosition(position, enabledItems)
			}(pos)
		}

		// 等待所有表位检测完成
		wg.Wait()

		// 生成整体检测结果
		overallResults := d.generateOverallResults(enabledPositions, enabledItems)

		// 记录整体检测结果
		fmt.Println("生成整体检测结果:", overallResults != nil)
		if overallResults != nil {
			fmt.Printf("整体检测结果包含 %d 个表位的数据\n", len(overallResults["results"].(map[string]interface{})))
		}

		// 关闭所有客户端
		d.closeClients()

		// 发送整体检测结果
		if d.resultCallback != nil && overallResults != nil {
			// 打印整体检测结果的内容
			fmt.Println("整体检测结果内容:")
			fmt.Printf("  - status: %s\n", overallResults["status"])
			fmt.Printf("  - timestamp: %d\n", overallResults["timestamp"])
			fmt.Printf("  - positions: %d\n", overallResults["positions"])
			fmt.Printf("  - items: %d\n", overallResults["items"])

			// 打印摘要信息
			if summary, ok := overallResults["summary"].(map[string]interface{}); ok {
				fmt.Println("  - summary:")
				for k, v := range summary {
					fmt.Printf("    - %s: %v\n", k, v)
				}
			}

			// 打印结果信息
			if results, ok := overallResults["results"].(map[string]interface{}); ok {
				fmt.Printf("  - results: 包含 %d 个表位\n", len(results))
				for posKey, posData := range results {
					if posDataMap, ok := posData.(map[string]interface{}); ok {
						fmt.Printf("    - %s: position_id=%v, success=%v, failed=%v, total=%v\n",
							posKey, posDataMap["position_id"], posDataMap["success"], posDataMap["failed"], posDataMap["total"])
					}
				}
			}

			// 序列化为JSON
			jsonData, err := json.Marshal(overallResults)
			if err != nil {
				fmt.Println("序列化整体检测结果失败:", err)
			} else {
				fmt.Printf("发送整体检测结果，长度: %d 字节\n", len(jsonData))
				fmt.Printf("整体检测结果JSON: %s\n", string(jsonData))

				// 确保在发送整体检测结果和完成状态之间有足够的时间间隔
				d.resultCallback(jsonData)

				// 等待一段时间，确保整体检测结果被处理
				time.Sleep(1000 * time.Millisecond)
			}
		} else {
			if d.resultCallback == nil {
				fmt.Println("结果回调函数为空，无法发送整体检测结果")
			}
			if overallResults == nil {
				fmt.Println("整体检测结果为空，无法发送")
			}
		}

		// 检测完成
		if d.resultCallback != nil {
			fmt.Println("发送检测完成状态")
			d.resultCallback([]byte(`{"status": "completed"}`))
		}
	}()

	return nil
}

// initClients 初始化所有表位的客户端
func (d *DLT698Detector) initClients(positions []config.PositionConfig) error {
	for _, pos := range positions {
		// 创建Pins串口配置
		pinsConfig := common.SerialConfig{
			Port:     fmt.Sprintf("%v", pos.SerialPins[0]),
			BaudRate: toInt(pos.SerialPins[1]),
			DataBits: toInt(pos.SerialPins[2]),
			StopBits: toInt(pos.SerialPins[3]),
			Parity:   fmt.Sprintf("%v", pos.SerialPins[4]),
			Timeout:  1000, // 1秒超时
		}

		// 创建RS485串口配置
		rs485Config := common.SerialConfig{
			Port:     fmt.Sprintf("%v", pos.SerialRs485[0]),
			BaudRate: toInt(pos.SerialRs485[1]),
			DataBits: toInt(pos.SerialRs485[2]),
			StopBits: toInt(pos.SerialRs485[3]),
			Parity:   fmt.Sprintf("%v", pos.SerialRs485[4]),
			Timeout:  1000, // 1秒超时
		}

		// 创建DL/T 698.45客户端
		serverAddr := []byte{0x01} // 默认服务器地址
		logicalAddr := byte(0x00)  // 默认逻辑地址
		client := dlt698.NewClient(pinsConfig, serverAddr, logicalAddr)
		client.SetTimeout(3 * time.Second)

		// 创建RS485串口
		rs485Port := common.NewSerialPort(rs485Config)
		pinPort := common.NewSerialPort(pinsConfig)

		// 保存客户端和串口
		d.clients[pos.ID] = client
		d.TTLPorts[pos.ID] = pinPort
		d.Rs485Port[pos.ID] = rs485Port
	}

	return nil
}

// closeClients 关闭所有客户端和串口
func (d *DLT698Detector) closeClients() {
	// 关闭所有DL/T 698.45客户端
	for _, client := range d.clients {
		if client.IsConnected() {
			client.Disconnect()
		}
	}

	// 关闭所有RS485串口
	for _, port := range d.TTLPorts {
		if port.IsOpen() {
			port.Close()
		}
	}

	// 记录关闭信息
	if d.resultCallback != nil {
		closeMsg := map[string]interface{}{
			"status":    "ports_closed",
			"timestamp": time.Now().Unix(),
			"message":   "所有串口已关闭",
		}
		closeData, _ := json.Marshal(closeMsg)
		d.resultCallback(closeData)
	}

	// 清空客户端和串口映射
	d.clients = make(map[int]*dlt698.Client)
	d.TTLPorts = make(map[int]*common.SerialPort)
}

// runDetectionForPosition 为单个表位执行检测
func (d *DLT698Detector) runDetectionForPosition(position config.PositionConfig, items []config.ItemConfig) {
	// 获取客户端
	client, ok := d.clients[position.ID]
	if !ok {
		if d.resultCallback != nil {
			errMsg := fmt.Sprintf(`{"error": "表位%d的客户端未初始化", "position_id": %d, "status": "error"}`, position.ID, position.ID)
			d.resultCallback([]byte(errMsg))
		}
		return
	}

	// 获取RS485串口
	ttlPort, ok := d.TTLPorts[position.ID]
	if !ok {
		if d.resultCallback != nil {
			errMsg := fmt.Sprintf(`{"error": "表位%d的RS485串口未初始化", "position_id": %d, "status": "error"}`, position.ID, position.ID)
			d.resultCallback([]byte(errMsg))
		}
		return
	}

	// 打开串口
	err := ttlPort.Open()
	if err != nil {
		if d.resultCallback != nil {
			errMsg := fmt.Sprintf(`{"error": "打开表位%d的RS485串口失败: %v", "position_id": %d, "status": "error"}`, position.ID, err, position.ID)
			d.resultCallback([]byte(errMsg))
		}
		return
	}
	defer ttlPort.Close()

	// 连接DL/T 698.45客户端
	err = client.Connect()
	if err != nil {
		if d.resultCallback != nil {
			errMsg := fmt.Sprintf(`{"error": "连接表位%d的DL/T 698.45客户端失败: %v", "position_id": %d, "status": "error"}`, position.ID, err, position.ID)
			d.resultCallback([]byte(errMsg))
		}
		return
	}
	defer client.Disconnect()

	// 为每个检测项执行检测
	for _, item := range items {
		// 检查是否收到停止信号
		select {
		case <-d.stopChan:
			// 收到停止信号
			return
		default:
			// 继续检测
		}

		// 通知前端开始当前检测项
		if d.resultCallback != nil {
			itemStartMsg := map[string]interface{}{
				"status":      "item_started",
				"item_id":     item.ID,
				"position_id": position.ID,
				"description": item.Description,
				"timestamp":   time.Now().Unix(),
			}
			itemStartData, _ := json.Marshal(itemStartMsg)
			d.resultCallback(itemStartData)
		}

		// 根据检测项ID执行不同的检测
		var result DetectionResult
		var detectionErr error

		switch item.ID {
		case 1:
			// 参数读取设置-关联属性数据表配置检测
			result, detectionErr = d.runParameterReadTest(client)
		case 2:
			// 开关及控制检测
			result, detectionErr = d.runSwitchControlTest(client)
		case 3:
			// 有功功率控制检测
			result, detectionErr = d.runActivePowerControlTest(client, ttlPort, position.ID)
		case 4:
			// 无功功率控制检测
			result, detectionErr = d.runReactivePowerControlTest(client, ttlPort, position.ID)
		case 5:
			// 功率因数控制检测
			result, detectionErr = d.runPowerFactorControlTest(client, ttlPort, position.ID)
		case 6:
			// 事件记录检测
			result, detectionErr = d.runEventRecordTest(client, ttlPort, position.ID)
		default:
			detectionErr = fmt.Errorf("未知的检测项ID: %d", item.ID)
		}

		// 处理检测结果
		if detectionErr != nil {
			if d.resultCallback != nil {
				errMsg := fmt.Sprintf(`{"error": "表位%d的检测项%d执行失败: %v", "position_id": %d, "item_id": %d, "status": "error"}`, position.ID, item.ID, detectionErr, position.ID, item.ID)
				d.resultCallback([]byte(errMsg))
			}

			// 创建失败结果并保存
			failedResult := DetectionResult{
				Type:       fmt.Sprintf("检测项-%d", item.ID),
				Confidence: 0.0, // 失败项置信度设为0
				Metadata: map[string]interface{}{
					"position_id": position.ID,
					"item_id":     item.ID,
					"description": item.Description,
					"timestamp":   time.Now().Unix(),
					"error":       detectionErr.Error(),
					"test_steps": []map[string]interface{}{
						{
							"name":        "检测失败",
							"description": fmt.Sprintf("检测项%d执行失败", item.ID),
							"status":      "failed",
							"data": map[string]interface{}{
								"reason": detectionErr.Error(),
							},
						},
					},
				},
			}

			// 保存失败结果
			d.resultsMutex.Lock()
			if _, ok := d.results[position.ID]; !ok {
				d.results[position.ID] = make(map[int]DetectionResult)
			}
			d.results[position.ID][item.ID] = failedResult
			d.resultsMutex.Unlock()
		} else {
			// 添加元数据
			if result.Metadata == nil {
				result.Metadata = make(map[string]interface{})
			}
			result.Metadata["position_id"] = position.ID
			result.Metadata["item_id"] = item.ID
			result.Metadata["description"] = item.Description
			result.Metadata["timestamp"] = time.Now().Unix()

			// 保存检测结果
			d.resultsMutex.Lock()
			if _, ok := d.results[position.ID]; !ok {
				d.results[position.ID] = make(map[int]DetectionResult)
			}
			d.results[position.ID][item.ID] = result
			d.resultsMutex.Unlock()

			// 转换为JSON并发送
			jsonData, err := json.Marshal(result)
			if err == nil && d.resultCallback != nil {
				d.resultCallback(jsonData)
			}
		}

		// 通知前端当前检测项完成
		if d.resultCallback != nil {
			itemCompleteMsg := map[string]interface{}{
				"status":      "item_completed",
				"item_id":     item.ID,
				"position_id": position.ID,
				"description": item.Description,
				"timestamp":   time.Now().Unix(),
			}
			itemCompleteData, _ := json.Marshal(itemCompleteMsg)
			d.resultCallback(itemCompleteData)
		}

		// 检测项之间稍作停顿
		time.Sleep(500 * time.Millisecond)
	}

}

// runParameterReadTest 执行参数读取设置-关联属性数据表配置检测
// TODO 应添加逆变器数据设置逻辑，在读取前先预设逆变器数据
func (d *DLT698Detector) runParameterReadTest(client *dlt698.Client) (DetectionResult, error) {
	// 1. 获取通讯地址，OAD：40010200
	serverAddr := []byte{0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA}
	logicalAddr := byte(0x00)
	// 发送请求并解析响应
	response, err := client.GetRequestNormal(dlt698.SAddrTypeSingle, dlt698.SAddrLogical0, serverAddr, logicalAddr, &dlt698.OAD{OI: 0x4001, AttributeID: 0x02, AttributeIndex: 0x00})
	if err != nil {
		return DetectionResult{}, fmt.Errorf("发送读取通讯地址请求失败: %v", err)
	}
	var moduleAddr string
	if v, ok := response["vale"].(string); ok {
		moduleAddr = v
	} else {
		return DetectionResult{}, fmt.Errorf("解析通讯地址失败: %v", response)
	}
	serverAddr = utils.ReverseStringToBytes(moduleAddr)

	// 2. 读取扩展模组时间日期
	response, err = client.GetRequestNormal(dlt698.SAddrTypeSingle, dlt698.SAddrLogical0, serverAddr, logicalAddr, &dlt698.OAD{OI: 0x4000, AttributeID: 0x02, AttributeIndex: 0x00})
	if err != nil {
		return DetectionResult{}, fmt.Errorf("发送读取扩展模组时间日期请求失败: %v", err)
	}
	var moduleTime string
	if v, ok := response["vale"].(string); ok {
		moduleTime = v
	} else {
		return DetectionResult{}, fmt.Errorf("解析扩展模组时间日期失败: %v", response)
	}
	logger.Info("扩展模组时间日期: %s", moduleTime)

	// 3. 读取光伏设备信息
	// 3.1. 设置逆变器序列号、有功功率、无功功率和逆变器类型
	// 3.1.1. 设置逆变器序列号
	// inverterSerialNumber := "1234567890"
	// client.in

	// 3.2. 读取光伏设备信息（698）
	// 3.3. 对比读取结果是否正确

	result := DetectionResult{
		Type:       "参数读取设置-关联属性数据表配置检测",
		Confidence: 0.0,
	}

	metadata := map[string]interface{}{
		"test_steps": []map[string]interface{}{},
	}

	// 时钟参数读取（光伏模组通信） - 抄读扩展模组日期时间，OAD：40000200
	res, err := client.GetData([]uint16{0x4000})
	if err != nil {
		return DetectionResult{}, fmt.Errorf("读取日期时间失败: %v", err)
	}
	logger.Info("读取日期时间成功: %v", res)
	// "data": map[string]interface{}{
	// 	"year":   2023,
	// 	"month":  5,
	// 	"day":    15,
	// 	"hour":   14,
	// 	"minute": 30,
	// 	"second": 45,
	// }
	metadata["test_steps"] = append(metadata["test_steps"].([]map[string]interface{}), map[string]interface{}{
		"name":        "时钟参数读取",
		"description": "抄读扩展模组日期时间，OAD：40000200",
		"status":      "success",
		"data":        res,
	})

	// 读取光伏设备信息（逆变器通信） - 使用OAD：48200200读取逆变器信息
	res, err = client.GetData([]uint16{0x4820})
	if err != nil {
		return DetectionResult{}, fmt.Errorf("读取逆变器信息失败: %v", err)
	}
	logger.Info("读取逆变器信息成功: %v", res)
	// "data": map[string]interface{}{
	// 	"serial_number":        "PV20230515001",
	// 	"rated_active_power":   10000,
	// 	"rated_reactive_power": 5000,
	// 	"inverter_type":        "三相",
	// },
	metadata["test_steps"] = append(metadata["test_steps"].([]map[string]interface{}), map[string]interface{}{
		"name":        "读取光伏设备信息",
		"description": "使用OAD：48200200读取逆变器信息",
		"status":      "success",
		"data":        res,
	})

	// 读电压 - 使用OAD：20000200读取逆变器电压值
	res, err = client.GetData([]uint16{0x2000})
	if err != nil {
		return DetectionResult{}, fmt.Errorf("读取逆变器电压值失败: %v", err)
	}
	logger.Info("读取逆变器电压值成功: %v", res)
	// "data": map[string]interface{}{
	// 	"voltage": 220.5,
	// },
	metadata["test_steps"] = append(metadata["test_steps"].([]map[string]interface{}), map[string]interface{}{
		"name":        "读电压",
		"description": "使用OAD：20000200读取逆变器电压值",
		"status":      "passed",
		"data":        res,
	})

	// 读取电流 - 使用OAD：20010200读取逆变器电流值
	res, err = client.GetData([]uint16{0x2001})
	if err != nil {
		return DetectionResult{}, fmt.Errorf("读取逆变器电流值失败: %v", err)
	}
	logger.Info("读取逆变器电流值成功: %v", res)
	// "data": map[string]interface{}{
	// 	"current": 45.2,
	// },
	metadata["test_steps"] = append(metadata["test_steps"].([]map[string]interface{}), map[string]interface{}{
		"name":        "读取电流",
		"description": "使用OAD：20010200读取逆变器电流值",
		"status":      "passed",
		"data":        res,
	})

	// 读取功率因数 - 使用OAD：200A0200读取逆变器功率因数
	res, err = client.GetData([]uint16{0x200A})
	if err != nil {
		return DetectionResult{}, fmt.Errorf("读取逆变器功率因数失败: %v", err)
	}
	logger.Info("读取逆变器功率因数成功: %v", res)
	// "data": map[string]interface{}{
	// 	"power_factor": 0.95,
	// },
	metadata["test_steps"] = append(metadata["test_steps"].([]map[string]interface{}), map[string]interface{}{
		"name":        "读取功率因数",
		"description": "使用OAD：200A0200读取逆变器功率因数",
		"status":      "passed",
		"data":        res,
	})
	return result, nil
}

// runSwitchControlTest 执行开关及控制检测
// TODO 应添加逆变器数据设置逻辑，在读取前先预设逆变器数据
func (d *DLT698Detector) runSwitchControlTest(client *dlt698.Client) (DetectionResult, error) {
	result := DetectionResult{
		Type:       "开关及控制检测",
		Confidence: 0.0,
	}

	// 控制关机 - 给光伏模组发操作逆变器开关机命令，OMD：48207F00，参数选择'关机（1）'
	err := client.Action(0x4820, 0x7F, 1)
	if err != nil {
		return DetectionResult{}, fmt.Errorf("控制关机失败: %v", err)
	}
	logger.Info("控制关机成功")
	result.Confidence += 0.25
	result.Metadata = map[string]interface{}{
		"test_steps": []map[string]interface{}{
			{
				"name":        "控制关机",
				"description": "给光伏模组发操作逆变器开关机命令，OMD：48207F00，参数选择'关机（1）'",
				"status":      "success",
				"data": map[string]interface{}{
					"command":  "关机",
					"response": "成功",
				},
			},
		},
	}

	// 控制开机 - 给光伏模组发操作逆变器开关机命令，OMD：48207F00，参数选择'开机（0）'
	err = client.Action(0x4820, 0x7F, 0)
	if err != nil {
		return DetectionResult{}, fmt.Errorf("控制开机失败: %v", err)
	}
	logger.Info("控制开机成功")
	result.Confidence += 0.25
	result.Metadata["test_steps"] = append(result.Metadata["test_steps"].([]map[string]interface{}), map[string]interface{}{
		"name":        "控制开机",
		"description": "给光伏模组发操作逆变器开关机命令，OMD：48207F00，参数选择'开机（0）'",
		"status":      "success",
		"data": map[string]interface{}{
			"command":  "开机",
			"response": "成功",
		},
	})

	// 事件记录 - 读取扩展模组中光伏开关机事件关联对象属性表（OAD：30680300）
	res, err := client.GetData([]uint16{0x3068})
	if err != nil {
		return DetectionResult{}, fmt.Errorf("读取扩展模组中光伏开关机事件关联对象属性表失败: %v", err)
	}
	logger.Info("读取扩展模组中光伏开关机事件关联对象属性表成功: %v", res)
	result.Confidence += 0.5
	// "data": map[string]interface{}{
	// 	"event_record": "有效",
	// },
	result.Metadata["test_steps"] = append(result.Metadata["test_steps"].([]map[string]interface{}), map[string]interface{}{
		"name":        "事件记录",
		"description": "读取扩展模组中光伏开关机事件关联对象属性表（OAD：30680300）",
		"status":      "success",
		"data":        res,
	})

	return result, nil
}

// runActivePowerControlTest 执行有功功率控制检测
func (d *DLT698Detector) runActivePowerControlTest(client *dlt698.Client, rs485Port *common.SerialPort, positionID int) (DetectionResult, error) {
	result := DetectionResult{
		Type:       "有功功率控制检测",
		Confidence: 0.90,
		Metadata: map[string]interface{}{
			"test_steps": []map[string]interface{}{
				{
					"name":        "有功功率控制",
					"description": "给光伏模组发操作逆变器有功功率控制命令，OMD：48208000，参数选择'有功功率（1）'",
					"status":      "success",
					"data": map[string]interface{}{
						"command":  "有功功率控制",
						"value":    10000,
						"response": "成功",
					},
				},
				{
					"name":        "事件记录",
					"description": "读取扩展模组中光伏有功功率控制事件关联对象属性表（OAD：30610300）",
					"status":      "success",
					"data": map[string]interface{}{
						"event_record":   "有效",
						"voltage":        220.5,
						"current":        45.2,
						"active_power":   10000,
						"reactive_power": 5000,
						"power_factor":   0.95,
					},
				},
			},
		},
	}

	// 模拟检测过程
	time.Sleep(1 * time.Second)

	return result, nil
}

// runReactivePowerControlTest 执行无功功率控制检测
func (d *DLT698Detector) runReactivePowerControlTest(client *dlt698.Client, rs485Port *common.SerialPort, positionID int) (DetectionResult, error) {
	result := DetectionResult{
		Type:       "无功功率控制检测",
		Confidence: 0.88,
		Metadata: map[string]interface{}{
			"test_steps": []map[string]interface{}{
				{
					"name":        "无功功率控制",
					"description": "给光伏模组发操作逆变器无功功率控制命令，OMD：48208000，参数选择'无功功率（2）'",
					"status":      "success",
					"data": map[string]interface{}{
						"command":  "无功功率控制",
						"value":    5000,
						"response": "成功",
					},
				},
				{
					"name":        "事件记录",
					"description": "读取扩展模组中光伏无功功率控制事件关联对象属性表（OAD：30620300）",
					"status":      "success",
					"data": map[string]interface{}{
						"event_record":   "有效",
						"voltage":        220.5,
						"current":        45.2,
						"active_power":   10000,
						"reactive_power": 5000,
						"power_factor":   0.95,
					},
				},
			},
		},
	}

	// 模拟检测过程
	time.Sleep(1 * time.Second)

	return result, nil
}

// runPowerFactorControlTest 执行功率因数控制检测
func (d *DLT698Detector) runPowerFactorControlTest(client *dlt698.Client, rs485Port *common.SerialPort, positionID int) (DetectionResult, error) {
	result := DetectionResult{
		Type:       "功率因数控制检测",
		Confidence: 0.85,
		Metadata: map[string]interface{}{
			"test_steps": []map[string]interface{}{
				{
					"name":        "功率因数控制",
					"description": "给光伏模组发操作逆变器功率因数控制命令，OMD：48208000，参数选择'功率因数（3）'",
					"status":      "success",
					"data": map[string]interface{}{
						"command":  "功率因数控制",
						"value":    0.9,
						"response": "成功",
					},
				},
				{
					"name":        "事件记录",
					"description": "读取扩展模组中光伏功率因数控制事件关联对象属性表（OAD：30630300）",
					"status":      "success",
					"data": map[string]interface{}{
						"event_record":   "有效",
						"voltage":        220.5,
						"current":        45.2,
						"active_power":   10000,
						"reactive_power": 5000,
						"power_factor":   0.9,
					},
				},
			},
		},
	}

	// 模拟检测过程
	time.Sleep(1 * time.Second)

	return result, nil
}

// runEventRecordTest 执行事件记录检测
func (d *DLT698Detector) runEventRecordTest(client *dlt698.Client, rs485Port *common.SerialPort, positionID int) (DetectionResult, error) {
	result := DetectionResult{
		Type:       "事件记录检测",
		Confidence: 0.93,
		Metadata: map[string]interface{}{
			"test_steps": []map[string]interface{}{
				{
					"name":        "校时事件",
					"description": "给扩展模组发明文广播校时，再抄读模组的日期时间",
					"status":      "success",
					"data": map[string]interface{}{
						"command":         "校时",
						"response":        "成功",
						"time_difference": 30,
					},
				},
				{
					"name":        "清零事件",
					"description": "控制台体给给扩展模组执行数据初始化命令（相当于以前电表清零命令）",
					"status":      "success",
					"data": map[string]interface{}{
						"command":  "清零",
						"response": "成功",
					},
				},
				{
					"name":        "模组数据清零",
					"description": "执行升级操作",
					"status":      "success",
					"data": map[string]interface{}{
						"command":        "升级",
						"response":       "成功",
						"before_version": "1.0.0",
						"after_version":  "1.1.0",
						"upgrade_result": "升级成功",
					},
				},
			},
		},
	}

	// 模拟检测过程
	time.Sleep(1 * time.Second)

	return result, nil
}

// Stop 停止检测过程
func (d *DLT698Detector) Stop() error {
	d.mutex.Lock()

	if !d.running {
		d.mutex.Unlock()
		return fmt.Errorf("检测器未在运行中")
	}

	// 检查通道是否已关闭
	select {
	case <-d.stopChan:
		// 通道已关闭，重新创建
		d.stopChan = make(chan struct{})
		d.mutex.Unlock()
		return nil
	default:
		// 通道未关闭，发送停止信号
		close(d.stopChan)
		d.mutex.Unlock()

		// 等待一小段时间，确保停止信号被处理
		time.Sleep(100 * time.Millisecond)
		return nil
	}
}

// generateOverallResults 生成整体检测结果
func (d *DLT698Detector) generateOverallResults(positions []config.PositionConfig, items []config.ItemConfig) map[string]interface{} {
	d.resultsMutex.Lock()
	defer d.resultsMutex.Unlock()

	// 如果没有结果，返回nil
	if len(d.results) == 0 {
		return nil
	}

	// 创建整体结果
	overallResults := map[string]interface{}{
		"status":    "overall_results", // 确保status字段存在且值为"overall_results"
		"timestamp": time.Now().Unix(),
		"positions": len(positions),
		"items":     len(items),
		"results":   make(map[string]interface{}),
		"summary":   make(map[string]interface{}),
	}

	// 统计成功和失败的检测项
	totalTests := 0
	successTests := 0
	failedTests := 0

	// 处理每个表位的结果
	for _, position := range positions {
		if !position.Enable {
			continue
		}

		positionResults, ok := d.results[position.ID]
		if !ok {
			continue
		}

		// 创建表位结果
		positionResult := map[string]interface{}{
			"position_id": position.ID,
			"items":       make(map[string]interface{}),
			"success":     0,
			"failed":      0,
			"total":       0,
		}

		// 记录表位结果
		fmt.Printf("处理表位 %d 的结果，包含 %d 个检测项\n", position.ID, len(positionResults))

		// 处理每个检测项的结果
		for _, item := range items {
			if !item.Enable {
				continue
			}

			totalTests++
			positionResult["total"] = positionResult["total"].(int) + 1

			// 获取检测项结果
			result, ok := positionResults[item.ID]
			if !ok {
				failedTests++
				positionResult["failed"] = positionResult["failed"].(int) + 1

				// 为未检测项添加失败结果数据
				itemKey := fmt.Sprintf("item_%d", item.ID)

				// 创建一个包含错误信息的元数据
				metadata := map[string]interface{}{
					"position_id": position.ID,
					"item_id":     item.ID,
					"description": item.Description,
					"timestamp":   time.Now().Unix(),
					"error":       "中断检测",
					"test_steps": []map[string]interface{}{
						{
							"name":        "检测中断",
							"description": "检测过程被用户中断",
							"status":      "failed",
							"data": map[string]interface{}{
								"reason": "中断检测",
							},
						},
					},
				}

				// 添加失败的检测项结果
				positionResult["items"].(map[string]interface{})[itemKey] = map[string]interface{}{
					"item_id":     item.ID,
					"description": item.Description,
					"confidence":  0.0, // 失败项置信度设为0
					"metadata":    metadata,
					"success":     false,
					"error":       "中断检测",
				}
				continue
			}

			// 添加检测项结果
			itemKey := fmt.Sprintf("item_%d", item.ID)
			positionResult["items"].(map[string]interface{})[itemKey] = map[string]interface{}{
				"item_id":     item.ID,
				"description": item.Description,
				"confidence":  result.Confidence,
				"metadata":    result.Metadata,
				"success":     result.Confidence >= 0.7, // 添加明确的成功/失败标志
			}

			// 统计成功和失败
			if result.Confidence >= 0.7 { // 假设0.7是成功的阈值
				successTests++
				positionResult["success"] = positionResult["success"].(int) + 1

				// 确保成功项有success标记
				itemKey := fmt.Sprintf("item_%d", item.ID)
				itemData := positionResult["items"].(map[string]interface{})[itemKey].(map[string]interface{})
				itemData["success"] = true
			} else {
				failedTests++
				positionResult["failed"] = positionResult["failed"].(int) + 1

				// 确保失败项有success和error标记
				itemKey := fmt.Sprintf("item_%d", item.ID)
				itemData := positionResult["items"].(map[string]interface{})[itemKey].(map[string]interface{})
				itemData["success"] = false

				// 如果元数据中有错误信息，则添加到error字段
				if metadata, ok := itemData["metadata"].(map[string]interface{}); ok {
					if errMsg, ok := metadata["error"].(string); ok {
						itemData["error"] = errMsg
					} else {
						itemData["error"] = "检测失败，置信度低于阈值"
					}
				} else {
					itemData["error"] = "检测失败，置信度低于阈值"
				}
			}
		}

		// 添加表位结果
		overallResults["results"].(map[string]interface{})[fmt.Sprintf("position_%d", position.ID)] = positionResult
	}

	// 添加摘要信息
	overallResults["summary"] = map[string]interface{}{
		"total_tests":   totalTests,
		"success_tests": successTests,
		"failed_tests":  failedTests,
		"success_rate":  float64(successTests) / float64(totalTests) * 100,
	}

	return overallResults
}

// toInt 将接口类型转换为int类型
func toInt(value interface{}) int {
	switch v := value.(type) {
	case int:
		return v
	case int8:
		return int(v)
	case int16:
		return int(v)
	case int32:
		return int(v)
	case int64:
		return int(v)
	case uint:
		return int(v)
	case uint8:
		return int(v)
	case uint16:
		return int(v)
	case uint32:
		return int(v)
	case uint64:
		return int(v)
	case float32:
		return int(v)
	case float64:
		return int(v)
	case string:
		i, _ := strconv.Atoi(v)
		return i
	default:
		// 尝试使用fmt.Sprintf和strconv.Atoi
		str := fmt.Sprintf("%v", v)
		i, _ := strconv.Atoi(str)
		return i
	}
}
