package pvdim

// Rectangle 表示一个矩形区域
type Rectangle struct {
	X      int `json:"x"`
	Y      int `json:"y"`
	Width  int `json:"width"`
	Height int `json:"height"`
}

// DetectionResult 表示检测结果
type DetectionResult struct {
	Type       string                 `json:"type"`               // 模块类型
	Confidence float64                `json:"confidence"`         // 置信度 (0-1)
	Location   Rectangle              `json:"location"`           // 位置信息
	Metadata   map[string]interface{} `json:"metadata,omitempty"` // 额外元数据
}

// DetectionInput 表示检测输入
type DetectionInput struct {
	SourceType string `json:"source_type"` // 输入源类型 (camera, file, stream)
	SourcePath string `json:"source_path"` // 输入源路径
}

// ModuleType 表示能源模块类型
type ModuleType string

// 预定义的模块类型
const (
	ModuleTypeSolar   ModuleType = "solar"   // 太阳能
	ModuleTypeWind    ModuleType = "wind"    // 风能
	ModuleTypeHydro   ModuleType = "hydro"   // 水能
	ModuleTypeThermal ModuleType = "thermal" // 热能
	ModuleTypeBattery ModuleType = "battery" // 电池储能
	ModuleTypeUnknown ModuleType = "unknown" // 未知类型
)
