package pvdim

// DetectionResult 表示检测结果
type DetectionResult struct {
	Type       string                 `json:"type"`               // 测试类型
	Confidence float64                `json:"confidence"`         // 测试结果
	Metadata   map[string]interface{} `json:"metadata,omitempty"` // 额外元数据
}

// DetectionInput 表示检测输入
type DetectionInput struct {
	SourceType string `json:"source_type"` // 输入源类型 (camera, file, stream)
	SourcePath string `json:"source_path"` // 输入源路径
}

// ModuleType 表示能源模块类型
type ModuleType string

// 预定义的模块类型
const ()
