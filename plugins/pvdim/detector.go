package pvdim

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"plugin.PVDIM/internal/config"
	"plugin.PVDIM/internal/logger"
)

// ResultCallback 是检测结果回调函数类型
type ResultCallback func([]byte)

// DetectorInterface 定义检测器接口
type DetectorInterface interface {
	SetResultCallback(callback ResultCallback)
	RunAsync() error
	Stop() error
}

// Detector 表示模块检测器
type Detector struct {
	config         *config.Config
	resultCallback ResultCallback
	stopChan       chan struct{}
	running        bool
	mutex          sync.Mutex
	dlt698Detector *DLT698Detector // DL/T 698.45协议检测器
}

// NewDetector 创建一个新的检测器实例
func NewDetector(cfg *config.Config) (*Detector, error) {
	// 创建DL/T 698.45协议检测器
	dlt698Detector, err := NewDLT698Detector(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建DL/T 698.45协议检测器失败: %w", err)
	}

	return &Detector{
		config:         cfg,
		stopChan:       make(chan struct{}),
		dlt698Detector: dlt698Detector,
	}, nil
}

// SetResultCallback 设置结果回调函数
func (d *Detector) SetResultCallback(callback ResultCallback) {
	d.resultCallback = callback
	// 同时设置DL/T 698.45协议检测器的回调函数
	if d.dlt698Detector != nil {
		d.dlt698Detector.SetResultCallback(callback)
	}
}

// Run 执行检测过程并返回结果
func (d *Detector) Run() ([]DetectionResult, error) {
	// 这里是检测的实现逻辑
	// 示例代码，实际应用中需要替换为真实的检测算法

	positions := d.config.GetPositions()
	detectionChannels := make([]chan bool, len(positions))

	for i, position := range positions {
		if position.Enable {
			detectionChannels[i] = make(chan bool)
			// go d.runDetectionForPosition(position, detectionChannels[i])
		}
	}

	// 等待接收开始检测消息
	for i := range detectionChannels {
		if detectionChannels[i] != nil {
			<-detectionChannels[i]
		}
	}

	results := []DetectionResult{
		{
			Type:       "太阳能模块",
			Confidence: 0.95,
			Location:   Rectangle{X: 10, Y: 20, Width: 100, Height: 200},
		},
		{
			Type:       "风能模块",
			Confidence: 0.87,
			Location:   Rectangle{X: 150, Y: 30, Width: 120, Height: 180},
		},
	}

	return results, nil
}

// RunAsync 异步执行检测过程，通过回调函数返回结果
func (d *Detector) RunAsync() error {
	d.mutex.Lock()
	if d.running {
		d.mutex.Unlock()
		logger.Warn("检测器已在运行中，拒绝重复启动")
		return fmt.Errorf("检测器已在运行中")
	}
	d.running = true
	d.mutex.Unlock()

	logger.Info("开始异步执行检测")

	// 使用DL/T 698.45协议检测器执行检测
	if d.dlt698Detector != nil {
		logger.Info("使用DL/T 698.45协议检测器执行检测")
		return d.dlt698Detector.RunAsync()
	}

	// 如果没有DL/T 698.45协议检测器，则使用默认检测逻辑
	logger.Info("使用默认检测逻辑执行检测")

	// 重置停止通道
	d.stopChan = make(chan struct{})

	// 在后台运行检测
	go func() {
		defer func() {
			d.mutex.Lock()
			d.running = false
			d.mutex.Unlock()
		}()

		// 获取启用的表位
		positions := d.config.GetPositions()
		enabledPositions := []config.PositionConfig{}
		for _, pos := range positions {
			if pos.Enable {
				enabledPositions = append(enabledPositions, pos)
				logger.Debug("启用表位: ID=%d", pos.ID)
			}
		}
		logger.Info("检测配置: 启用表位数量=%d", len(enabledPositions))

		// 获取启用的检测项
		items := d.config.GetItems()
		enabledItems := []config.ItemConfig{}
		for _, item := range items {
			if item.Enable {
				enabledItems = append(enabledItems, item)
				logger.Debug("启用检测项: ID=%d, 描述=%s", item.ID, item.Description)
			}
		}
		logger.Info("检测配置: 启用检测项数量=%d", len(enabledItems))

		// 如果没有启用的表位，返回错误
		if len(enabledPositions) == 0 {
			errMsg := "没有启用的表位"
			logger.Error(errMsg)
			if d.resultCallback != nil {
				d.resultCallback([]byte(`{"error": "` + errMsg + `"}`))
			}
			return
		}

		// 如果没有启用的检测项，返回错误
		if len(enabledItems) == 0 {
			errMsg := "没有启用的检测项"
			logger.Error(errMsg)
			if d.resultCallback != nil {
				d.resultCallback([]byte(`{"error": "` + errMsg + `"}`))
			}
			return
		}

		// 通知前端开始检测
		timestamp := time.Now().Unix()
		logger.Info("开始检测: 表位数量=%d, 检测项数量=%d, 时间戳=%d",
			len(enabledPositions), len(enabledItems), timestamp)

		if d.resultCallback != nil {
			startMsg := map[string]interface{}{
				"status":    "started",
				"positions": len(enabledPositions),
				"items":     len(enabledItems),
				"timestamp": timestamp,
			}
			startData, _ := json.Marshal(startMsg)
			d.resultCallback(startData)
		}

		// 为每个检测项执行检测
		for _, item := range enabledItems {
			// 检查是否收到停止信号
			select {
			case <-d.stopChan:
				// 收到停止信号
				logger.Info("检测过程收到停止信号，中断检测")
				if d.resultCallback != nil {
					d.resultCallback([]byte(`{"status": "stopped"}`))
				}
				return
			default:
				// 继续检测
			}

			// 记录开始检测当前项
			logger.Info("开始检测项: ID=%d, 描述=%s", item.ID, item.Description)

			// 通知前端开始当前检测项
			timestamp := time.Now().Unix()
			if d.resultCallback != nil {
				itemStartMsg := map[string]interface{}{
					"status":      "item_started",
					"item_id":     item.ID,
					"description": item.Description,
					"timestamp":   timestamp,
				}
				itemStartData, _ := json.Marshal(itemStartMsg)
				d.resultCallback(itemStartData)
			}

			// 模拟检测过程 - 每个检测项执行5次迭代
			for i := 0; i < 5; i++ {
				select {
				case <-d.stopChan:
					// 收到停止信号
					logger.Info("检测项执行过程中收到停止信号，中断检测")
					if d.resultCallback != nil {
						d.resultCallback([]byte(`{"status": "stopped"}`))
					}
					return
				default:
					// 继续检测
					time.Sleep(500 * time.Millisecond)
					logger.Debug("检测项 %d 迭代 %d/5", item.ID, i+1)

					// 为每个启用的表位生成随机检测结果
					for _, pos := range enabledPositions {
						// 生成随机检测结果
						confidence := 0.7 + float64(i)/10.0
						if confidence > 1.0 {
							confidence = 1.0
						}

						result := DetectionResult{
							Type:       fmt.Sprintf("检测项-%d", item.ID),
							Confidence: confidence,
							Location:   Rectangle{X: 10 * pos.ID, Y: 20 * pos.ID, Width: 100, Height: 200},
							Metadata: map[string]interface{}{
								"position_id":  pos.ID,
								"item_id":      item.ID,
								"description":  item.Description,
								"timestamp":    time.Now().Unix(),
								"iteration":    i,
								"total_items":  len(enabledItems),
								"current_item": item.ID,
							},
						}

						// 记录检测结果
						logger.Result(pos.ID, item.ID, confidence > 0.8,
							fmt.Sprintf("迭代=%d, 置信度=%.2f", i+1, confidence))

						// 转换为 JSON
						jsonData, err := json.Marshal(result)
						if err != nil {
							logger.Error("序列化检测结果失败: %v", err)
							continue
						}

						// 通过回调函数返回结果
						if d.resultCallback != nil {
							d.resultCallback(jsonData)
						}
					}
				}
			}

			// 记录检测项完成
			logger.Info("检测项完成: ID=%d, 描述=%s", item.ID, item.Description)

			// 通知前端当前检测项完成
			timestamp = time.Now().Unix()
			if d.resultCallback != nil {
				itemCompleteMsg := map[string]interface{}{
					"status":      "item_completed",
					"item_id":     item.ID,
					"description": item.Description,
					"timestamp":   timestamp,
				}
				itemCompleteData, _ := json.Marshal(itemCompleteMsg)
				d.resultCallback(itemCompleteData)
			}

			// 检测项之间稍作停顿
			time.Sleep(1 * time.Second)
		}

		// 检测完成
		logger.Info("所有检测项完成，检测过程结束")
		if d.resultCallback != nil {
			d.resultCallback([]byte(`{"status": "completed"}`))
		}
	}()

	return nil
}

// Stop 停止检测过程
func (d *Detector) Stop() error {
	d.mutex.Lock()

	if !d.running {
		d.mutex.Unlock()
		logger.Warn("检测器未在运行中，无法停止")
		return fmt.Errorf("检测器未在运行中")
	}

	logger.Info("正在停止检测过程")

	// 使用DL/T 698.45协议检测器停止检测
	if d.dlt698Detector != nil {
		logger.Info("使用DL/T 698.45协议检测器停止检测")
		d.mutex.Unlock()
		err := d.dlt698Detector.Stop()
		if err != nil {
			logger.Error("停止DL/T 698.45协议检测器失败: %v", err)
		} else {
			logger.Info("DL/T 698.45协议检测器已停止")
		}
		return err
	}

	// 如果没有DL/T 698.45协议检测器，则使用默认停止逻辑
	logger.Info("使用默认逻辑停止检测")

	// 检查通道是否已关闭
	select {
	case <-d.stopChan:
		// 通道已关闭，重新创建
		logger.Debug("停止通道已关闭，重新创建")
		d.stopChan = make(chan struct{})
		d.mutex.Unlock()
		return nil
	default:
		// 通道未关闭，发送停止信号
		logger.Debug("发送停止信号")
		close(d.stopChan)
		d.mutex.Unlock()

		// 等待一小段时间，确保停止信号被处理
		time.Sleep(100 * time.Millisecond)
		logger.Info("检测过程已停止")
		return nil
	}
}
