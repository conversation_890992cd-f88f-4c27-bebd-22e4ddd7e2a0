package main

import (
	"context"
	"fmt"
	"log"
	"plugin.PVDIM/shared"
	"strings"
	"time"

	"github.com/hashicorp/go-plugin"
)

// ExamplePlugin 示例插件实现
type ExamplePlugin struct {
	name        string
	version     string
	initialized bool
	config      map[string]string
}

// NewExamplePlugin 创建新的示例插件实例
func NewExamplePlugin() *ExamplePlugin {
	return &ExamplePlugin{
		name:    "example-plugin",
		version: "1.0.0",
		config:  make(map[string]string),
	}
}

// GetName 返回插件名称
func (p *ExamplePlugin) GetName() string {
	return p.name
}

// GetVersion 返回插件版本
func (p *ExamplePlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *ExamplePlugin) Initialize(config map[string]string) error {
	log.Printf("Initializing %s plugin with config: %v", p.name, config)
	
	p.config = config
	p.initialized = true
	
	// 可以在这里进行插件特定的初始化工作
	if greeting, exists := config["greeting"]; exists {
		log.Printf("Plugin greeting configured: %s", greeting)
	}
	
	return nil
}

// Execute 执行插件主要功能
func (p *ExamplePlugin) Execute(ctx context.Context, input string) (string, error) {
	if !p.initialized {
		return "", fmt.Errorf("plugin %s is not initialized", p.name)
	}

	log.Printf("Executing %s plugin with input: %s", p.name, input)

	// 检查上下文是否被取消
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	// 模拟一些处理时间
	time.Sleep(100 * time.Millisecond)

	// 根据输入执行不同的操作
	switch strings.ToLower(strings.TrimSpace(input)) {
	case "hello":
		greeting := p.config["greeting"]
		if greeting == "" {
			greeting = "Hello"
		}
		return fmt.Sprintf("%s from %s plugin (v%s)!", greeting, p.name, p.version), nil
		
	case "time":
		return fmt.Sprintf("Current time: %s", time.Now().Format("2006-01-02 15:04:05")), nil
		
	case "config":
		if len(p.config) == 0 {
			return "No configuration set", nil
		}
		var configStr strings.Builder
		configStr.WriteString("Plugin configuration:\n")
		for key, value := range p.config {
			configStr.WriteString(fmt.Sprintf("  %s: %s\n", key, value))
		}
		return configStr.String(), nil
		
	case "reverse":
		return "Please provide text to reverse after 'reverse:'", nil
		
	default:
		if strings.HasPrefix(strings.ToLower(input), "reverse:") {
			text := strings.TrimSpace(input[8:])
			if text == "" {
				return "No text provided to reverse", nil
			}
			return reverseString(text), nil
		}
		
		if strings.HasPrefix(strings.ToLower(input), "upper:") {
			text := strings.TrimSpace(input[6:])
			return strings.ToUpper(text), nil
		}
		
		if strings.HasPrefix(strings.ToLower(input), "lower:") {
			text := strings.TrimSpace(input[6:])
			return strings.ToLower(text), nil
		}
		
		// 默认回显输入
		return fmt.Sprintf("Echo from %s: %s", p.name, input), nil
	}
}

// Cleanup 清理插件资源
func (p *ExamplePlugin) Cleanup() error {
	log.Printf("Cleaning up %s plugin", p.name)
	
	// 在这里进行资源清理工作
	p.initialized = false
	p.config = make(map[string]string)
	
	return nil
}

// reverseString 反转字符串
func reverseString(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func main() {
	// 创建插件实例
	pluginImpl := NewExamplePlugin()

	// 创建插件映射
	pluginMap := map[string]plugin.Plugin{
		shared.PluginName: &shared.PluginGRPC{Impl: pluginImpl},
	}

	log.Printf("Starting %s plugin (v%s)", pluginImpl.GetName(), pluginImpl.GetVersion())

	// 启动插件服务
	plugin.Serve(&plugin.ServeConfig{
		HandshakeConfig: shared.Handshake,
		Plugins:         pluginMap,
		GRPCServer:      plugin.DefaultGRPCServer,
	})
}
